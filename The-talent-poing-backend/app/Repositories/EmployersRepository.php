<?php

namespace App\Repositories;

use App\Models\User;
use Illuminate\Contracts\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Repository for managing employer-related database operations
 *
 * This repository handles complex queries for retrieving employer data
 * with their associated companies, memberships, claims, and ratings.
 * Implements advanced filtering, sorting, and pagination capabilities.
 *
 * @package App\Repositories
 * <AUTHOR> Name
 * @since 1.0.0
 *
 * @see \App\Models\User
 * @see \App\Models\Company
 * @see \App\Models\Membership
 * @see \App\Models\ClaimCompany
 */
class EmployersRepository
{
    /**
     * Get employers with their companies, memberships, and claims based on various filters
     *
     * This method retrieves employers (users with role 'employer') along with their associated
     * company data, latest membership information, and priority-based claim status.
     * Each company appears only once in the results, represented by its owner.
     *
     * @param string|null $name Company name filter (partial match using LIKE)
     * @param array|null $accountTypes Array of plan IDs to filter by membership plan types
     * @param array $locations Array of location IDs to filter companies by location
     * @param string|null $activity Activity filter options:
     *                              - 'last_24_hours': Users who logged in within last 24 hours
     *                              - null: No activity filter
     * @param string|null $ratingFilter Company rating filter options:
     *                                  - '1_and_above': Companies with rating >= 1
     *                                  - '2_and_above': Companies with rating >= 2
     *                                  - '3_and_above': Companies with rating >= 3
     *                                  - '4_and_above': Companies with rating >= 4
     *                                  - '5': Companies with rating = 5
     *                                  - null: No rating filter
     * @param string|null $teamMemberSort Team size sorting options:
     *                                    - 'low_to_high': Sort by team size ascending
     *                                    - 'high_to_low': Sort by team size descending
     *                                    - null: No team size sorting
     * @param string|null $jobPostSort Job count sorting options:
     *                                 - 'low_to_high': Sort by active jobs count ascending
     *                                 - 'high_to_low': Sort by active jobs count descending
     *                                 - null: No job count sorting
     * @param int $pageSize Number of results per page (default: 10)
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator Paginated collection of employers
     *
     * @throws \Illuminate\Database\QueryException If database query fails
     *
     * @note Membership Logic:
     *       - Prioritizes non-expired memberships (expire_at IS NULL OR expire_at > NOW())
     *       - Falls back to latest membership if no active membership exists
     *
     * @note Claim Priority Logic:
     *       - approved (highest priority)
     *       - requested
     *       - rejected
     *       - null/unclaimed (lowest priority)
     *
     * @note Company Uniqueness:
     *       - Each company appears only once in results
     *       - Represented by the company owner (users.id = company.user_id)
     *       - Uses INNER JOIN to ensure only users with companies are included
     */
    public function getEmployersByParams(
        ?string $name = null,
        ?array $accountTypes = null,
        array $locations = [],
        ?string $activity = null,
        ?string $ratingSort = null,
        ?string $teamMemberSort = null,
        ?string $jobPostSort = null,
        int $pageSize = 10
    ) {
        $ratingsSubquery = DB::table('company_reviews')
            ->select(
                'company_id',
                DB::raw('AVG(rating) AS avg_rating'),
                DB::raw('COUNT(*) AS total_reviews_count')
            )
            ->groupBy('company_id');

        $jobsCount = DB::table('jobs')
            ->select('company_id', DB::raw('COUNT(*) AS jobs_count'))
            ->where('jobs.job_status', 'active')
            ->groupBy('company_id');
        
        $teamMembersCount = DB::table('users')
                ->select(
                    'created_by_id',
                    DB::raw('COUNT(*) as total_staff')
                )
                ->where([
                    'users.role' => 'staff',
                    'status' => 'active',
                ])
                ->groupBy('created_by_id');

        $query = User::query()
            ->join('company', 'users.id', '=', 'company.user_id')
            ->leftJoinSub($this->prepareSubQueryLatestMembership(), 'membership', 'company.id', '=', 'membership.company_id')
            ->leftJoin('plans', 'membership.plan_id', '=', 'plans.id')
            ->leftJoinSub($this->prepareSubQueryClaimStatus(), 'claim_companies', 'company.id', '=', 'claim_companies.company_id')
            ->leftJoin('users as claim_users', 'claim_companies.user_id', '=', 'claim_users.id')
            ->leftJoinSub($ratingsSubquery, 'ratings', function ($join) {
                $join->on('company.id', '=', 'ratings.company_id');
            })
            ->leftJoinSub($jobsCount, 'jc', function ($join) {
                $join->on('company.id', '=', 'jc.company_id');
            })
            ->leftJoinSub($teamMembersCount, 'tmc', function ($join) {
                $join->on('users.id', '=', 'tmc.created_by_id');
            })
            ->select([
                'users.*',
                'company.id AS current_company_id',
                'company.no_of_employees',
                'company.company_location',
                'company.company_name',
                'membership.id AS membership_id',
                'membership.plan_id AS membership_plan_id',
                'membership.expire_at AS membership_expire_at',
                'membership.purchase_at AS membership_purchase_at',
                'membership.status AS membership_status',
                'plans.id AS plan_id',
                'plans.plan_title',
                'plans.plan_sub_desc',
                'plans.plan_currency',
                'plans.plan_amount',
                'plans.plan_type',
                'plans.plan_points',
                'plans.status AS plan_status',
                'claim_companies.status AS claim_status',
                'claim_companies.id AS claim_companies_id',
                'claim_companies.message AS claim_companies_message',
                'claim_companies.created_at AS claim_companies_created_at',
                'claim_users.id AS claim_user_id',
                'claim_users.name AS claim_user_name',
                'claim_users.email AS claim_user_email',
                'claim_users.contact_no AS claim_user_phone',
                'claim_users.role AS claim_user_role',
                'claim_users.current_position AS claim_user_designation',
                'ratings.avg_rating',
                'ratings.total_reviews_count',
                DB::raw('COALESCE(jc.jobs_count, 0) AS jobs_count'),
                DB::raw('COALESCE(tmc.total_staff, 0) as total_staff')
            ])
            ->when($name, fn($q) => $q->where('company.company_name', 'like', "%{$name}%"))
            ->when($accountTypes, fn($q) => $q->whereIn('membership.plan_id', $accountTypes))
            ->when($locations, fn($q) => $q->whereIn('company.company_location', $locations))
            ->when($activity === 'last_24_hours', fn($q) => $q->where('users.last_login', '>=', now()->subDay()))
            ->when($ratingSort, function ($q) use ($ratingSort) {
                $value = match ($ratingSort) {
                    '1_and_above' => 1,
                    '2_and_above' => 2,
                    '3_and_above' => 3,
                    '4_and_above' => 4,
                    '5'           => 5,
                    default       => null,
                };
                if ($value !== null) {
                    $op = $ratingSort === '5' ? '=' : '>=';
                    $q->whereRaw("COALESCE(ratings.avg_rating, 0) {$op} ?", [$value]);
                }
            })
            ->where('users.role', 'employer')
            ->where('users.status', 'active');

        if ($ratingSort) {
            $query->orderByRaw("COALESCE(ratings.avg_rating, 0) ASC");
        } elseif ($teamMemberSort) {
            $dir = $teamMemberSort === 'low_to_high' ? 'ASC' : 'DESC';
            $query->orderByRaw("COALESCE(total_staff, 0) {$dir}");
        } elseif ($jobPostSort) {
            $dir = $jobPostSort === 'low_to_high' ? 'ASC' : 'DESC';
            $query->orderByRaw("COALESCE(jobs_count, 0) {$dir}");
        } else {
            $query->orderByDesc('users.id');
        }
        
        if ($query->get()->count() > $pageSize) {
            return $query->paginate($pageSize);
        }
        
        return $query->get();
    }

    /**
     * Get companies with their associated users, memberships, and claims based on various filters
     *
     * This method retrieves companies along with their associated user data (if assigned),
     * latest membership information, and priority-based claim status. The method includes
     * both companies with assigned managing users and companies without assigned users.
     *
     * @param string|null $name Company name filter (partial match using LIKE)
     * @param array|null $accountTypes Array of plan IDs to filter by membership plan types
     * @param array $locations Array of location IDs to filter companies by location
     * @param string|null $activity Activity filter options:
     *                              - 'last_24_hours': Users who logged in within last 24 hours
     *                              - null: No activity filter
     * @param string|null $ratingSort Company rating filter options:
     *                                  - '1_and_above': Companies with rating >= 1
     *                                  - '2_and_above': Companies with rating >= 2
     *                                  - '3_and_above': Companies with rating >= 3
     *                                  - '4_and_above': Companies with rating >= 4
     *                                  - '5': Companies with rating = 5
     *                                  - null: No rating filter
     * @param string|null $teamMemberSort Team size sorting options:
     *                                    - 'low_to_high': Sort by team size ascending
     *                                    - 'high_to_low': Sort by team size descending
     *                                    - null: No team size sorting
     * @param string|null $jobPostSort Job count sorting options:
     *                                 - 'low_to_high': Sort by active jobs count ascending
     *                                 - 'high_to_low': Sort by active jobs count descending
     *                                 - null: No job count sorting
     * @param int $pageSize Number of results per page (default: 10)
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Support\Collection Paginated collection or collection of companies
     *
     * @throws \Illuminate\Database\QueryException If database query fails
     *
     * @note Membership Logic:
     *       - Prioritizes non-expired memberships (expire_at IS NULL OR expire_at > NOW())
     *       - Falls back to latest membership if no active membership exists
     *
     * @note Claim Priority Logic:
     *       - approved (highest priority)
     *       - requested
     *       - rejected
     *       - null/unclaimed (lowest priority)
     *
     * @note Company Inclusion Logic:
     *       - All active companies are included (company.status = 'active')
     *       - Companies with assigned users (who must be active employers)
     *       - Companies without assigned users (user_id IS NULL)
     *       - Results formatted as User models for compatibility with EmployerResource
     */
    public function getCompaniesByParams(
        ?string $name = null,
        ?array $accountTypes = null,
        array $locations = [],
        ?string $activity = null,
        ?string $ratingSort = null,
        ?string $teamMemberSort = null,
        ?string $jobPostSort = null,
        int $pageSize = 10
    ) {
        $ratingsSubquery = DB::table('company_reviews')
            ->select(
                'company_id',
                DB::raw('AVG(rating) AS avg_rating'),
                DB::raw('COUNT(*) AS total_reviews_count')
            )
            ->groupBy('company_id');

        $jobsCount = DB::table('jobs')
            ->select('company_id', DB::raw('COUNT(*) AS jobs_count'))
            ->where('jobs.job_status', 'active')
            ->groupBy('company_id');

        $teamMembersCount = DB::table('users')
            ->select(
                'created_by_id',
                DB::raw('COUNT(*) as total_staff')
            )
            ->where([
                'users.role' => 'staff',
                'status' => 'active',
            ])
            ->groupBy('created_by_id');

        Log::info($name);

        $query = DB::table('company')
            ->leftJoin('users', 'company.user_id', '=', 'users.id')
            ->leftJoinSub($this->prepareSubQueryLatestMembership(), 'membership', 'company.id', '=', 'membership.company_id')
            ->leftJoin('plans', 'membership.plan_id', '=', 'plans.id')
            ->leftJoinSub($this->prepareSubQueryClaimStatus(), 'claim_companies', 'company.id', '=', 'claim_companies.company_id')
            ->leftJoin('users as claim_users', 'claim_companies.user_id', '=', 'claim_users.id')
            ->leftJoinSub($ratingsSubquery, 'ratings', function ($join) {
                $join->on('company.id', '=', 'ratings.company_id');
            })
            ->leftJoinSub($jobsCount, 'jc', function ($join) {
                $join->on('company.id', '=', 'jc.company_id');
            })
            ->leftJoinSub($teamMembersCount, 'tmc', function ($join) {
                $join->on('users.id', '=', 'tmc.created_by_id');
            })
            ->select([
                // User fields (NULL for companies without users)
                'users.id',
                'users.name',
                'users.email',
                'users.slug',
                'users.password',
                'users.view_password',
                'users.role',
                'users.company_id',
                'users.available_resume_count',
                'users.profile_image',
                'users.where_job_search',
                'users.job_type',
                'users.job_status',
                'users.where_currently_based',
                'users.cities',
                'users.countries',
                'users.current_position',
                'users.profile_complete_percentage',
                'users.unlock_instant_apply',
                'users.linked_id',
                'users.google_id',
                'users.email_verified_at',
                'users.contact_no',
                'users.login_count',
                'users.first_login',
                'users.is_approved',
                'users.date_of_birth',
                'users.gender',
                'users.years_of_experience',
                'users.current_salary',
                'users.desired_salary',
                'users.bio',
                'users.otp',
                'users.status',
                'users.created_by_id',
                'users.is2FA',
                'users.created_at',
                'users.updated_at',
                // Company fields
                'company.id AS current_company_id',
                'company.no_of_employees',
                'company.company_location',
                'company.company_name',
                'company.user_id as company_user_id',
                // Membership fields
                'membership.id AS membership_id',
                'membership.plan_id AS membership_plan_id',
                'membership.expire_at AS membership_expire_at',
                'membership.purchase_at AS membership_purchase_at',
                'membership.status AS membership_status',
                // Plan fields
                'plans.id AS plan_id',
                'plans.plan_title',
                'plans.plan_sub_desc',
                'plans.plan_currency',
                'plans.plan_amount',
                'plans.plan_type',
                'plans.plan_points',
                'plans.status AS plan_status',
                // Claim fields
                'claim_companies.status AS claim_status',
                'claim_companies.id AS claim_companies_id',
                'claim_companies.message AS claim_companies_message',
                'claim_companies.created_at AS claim_companies_created_at',
                'claim_users.id AS claim_user_id',
                'claim_users.name AS claim_user_name',
                'claim_users.email AS claim_user_email',
                'claim_users.contact_no AS claim_user_phone',
                'claim_users.role AS claim_user_role',
                'claim_users.current_position AS claim_user_designation',
                // Aggregated fields
                'ratings.avg_rating',
                'ratings.total_reviews_count',
                DB::raw('COALESCE(jc.jobs_count, 0) AS jobs_count'),
                DB::raw('COALESCE(tmc.total_staff, 0) as total_staff')
            ])
            ->when($name, fn($q) => $q->where('company.company_name', 'like', "%{$name}%"))
            ->when($accountTypes, fn($q) => $q->whereIn('membership.plan_id', $accountTypes))
            ->when($locations, fn($q) => $q->whereIn('company.company_location', $locations))
            ->when($activity === 'last_24_hours', fn($q) => $q->where('users.last_login', '>=', now()->subDay()))
            ->when($ratingSort, function ($q) use ($ratingSort) {
                $value = match ($ratingSort) {
                    '1_and_above' => 1,
                    '2_and_above' => 2,
                    '3_and_above' => 3,
                    '4_and_above' => 4,
                    '5'           => 5,
                    default       => null,
                };
                if ($value !== null) {
                    $op = $ratingSort === '5' ? '=' : '>=';
                    $q->whereRaw("COALESCE(ratings.avg_rating, 0) {$op} ?", [$value]);
                }
            })
            ->where('company.status', 'active')
            ->where(function ($q) {
                $q->where(function ($subQ) {
                    // Companies with assigned users who are employers and active
                    $subQ->whereNotNull('users.id');
//                        ->where('users.role', 'employer')
//                        ->where('users.status', 'active');
                })
                    ->orWhere(function ($subQ) {
                        // Companies without assigned users (user_id is NULL)
                        $subQ->whereNull('company.user_id');
                    });
            });

        // Apply sorting
        if ($ratingSort) {
            $query->orderByRaw("COALESCE(ratings.avg_rating, 0) ASC");
        } elseif ($teamMemberSort) {
            $dir = $teamMemberSort === 'low_to_high' ? 'ASC' : 'DESC';
            $query->orderByRaw("COALESCE(total_staff, 0) {$dir}");
        } elseif ($jobPostSort) {
            $dir = $jobPostSort === 'low_to_high' ? 'ASC' : 'DESC';
            $query->orderByRaw("COALESCE(jobs_count, 0) {$dir}");
        } else {
            $query->orderByDesc('company.id');
        }

        // Get results and convert to User models for compatibility with EmployerResource
        $results = collect($query->get())->map(function ($item) {
            $user = new User();

            // Convert stdClass to array for easier processing
            $properties = (array) $item;

            foreach ($properties as $key => $value) {
                $user->$key = $value;
            }

            $user->exists = true; // Mark as existing record to avoid save operations

            // For companies without users, create virtual user data
            if (!$user->id && $user->current_company_id) {
                $user->id = 'virtual_' . $user->current_company_id; // Temporary ID for virtual users
                $user->name = $user->company_name ? $user->company_name . ' (Company)' : 'Unknown Company';
                $user->email = null;
                $user->role = 'employer';
                $user->status = 'active';
                $user->created_at = now();
                $user->updated_at = now();
            }

            return $user;
        });

        // Apply pagination if needed
        if ($results->count() > $pageSize) {
            $currentPage = request()->get('page', 1);
            $offset = ($currentPage - 1) * $pageSize;
            $items = $results->slice($offset, $pageSize)->values();

            return new \Illuminate\Pagination\LengthAwarePaginator(
                $items,
                $results->count(),
                $pageSize,
                $currentPage,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        }

        return $results;
    }


    /**
     * Get a specific employer by ID with complete company, membership, and claim data
     *
     * This method retrieves a single employer (user with role 'employer') along with
     * their associated company information, latest membership details, and priority-based
     * claim status. The result is wrapped in an array for consistency with collection methods.
     *
     * @param int $id The user ID of the employer to retrieve
     *
     * @return array Array containing the employer user object with related data, or empty array if not found
     *
     * @throws \Illuminate\Database\QueryException If database query fails
     *
     * @note Uses the same subqueries as getEmployersByParams() for data consistency:
     *       - prepareSubQueryClaimStatus() for priority-based claim selection
     *       - prepareSubQueryLatestMembership() for optimal membership selection
     *
     * @note Returns array format for compatibility with EmployerResource::collection()
     *
     * @see getEmployersByParams() For bulk employer retrieval with filtering
     * @see prepareSubQueryClaimStatus() For claim priority logic
     * @see prepareSubQueryLatestMembership() For membership selection logic
     */
    public function getEmployersById(int $id): array
    {
        $employer = [];

        $user = User::with([
                'ownCompany.logo',
                'company',
                'company.sector',
                'company.location',
                'company.claim',
                'company.membership.plan'
            ])
            ->leftJoin('company', 'users.id', '=', 'company.user_id')
            ->leftJoin('locations', 'locations.id', '=', 'company.company_location')
            ->leftJoinSub($this->prepareSubQueryClaimStatus(), 'claim_companies', 'company.id', '=', 'claim_companies.company_id')
            ->leftJoinSub($this->prepareSubQueryLatestMembership(), 'membership', 'company.id', '=', 'membership.company_id')
            ->select(
                'users.*',
                'claim_companies.id as claim_companies_id',
                'claim_companies.status as claim_status',
                'claim_companies.user_id as claim_user_id',
                'claim_companies.message as claim_message',
                'claim_companies.created_at as claim_created_at',
                'membership.id as membership_id',
                'membership.plan_id as membership_plan_id',
                'membership.status as membership_status',
                'membership.expire_at as membership_expire_at',
                'membership.purchase_at as membership_purchase_at',
                'locations.location_name as city',
                DB::raw('CASE
                    WHEN claim_companies.status = "approved" THEN "Claimed"
                    WHEN claim_companies.status = "requested" THEN "Requested"
                    WHEN claim_companies.status = "rejected" THEN "Rejected"
                    ELSE "Unclaimed"
                END as company_type')
            )
            ->where('users.id', $id)
            ->first();
        array_push($employer, $user);
        
        return $employer;
    }

    /**
     * Prepare subquery for selecting the latest membership with priority for non-expired ones
     *
     * This method creates a sophisticated subquery that selects the most appropriate
     * membership for each company using a two-tier priority system:
     * 1. Non-expired memberships are prioritized over expired ones
     * 2. Among memberships of the same expiry status, the latest (highest ID) is selected
     *
     * The query uses a LEFT JOIN with exclusion pattern to efficiently find the "best"
     * membership without nested subqueries or complex COALESCE operations.
     *
     * @return \Illuminate\Database\Query\Builder Configured query builder for membership subquery
     *
     * @throws \Illuminate\Database\QueryException If database query fails
     *
     * @note Selection Logic:
     *       - Step 1: Among non-expired memberships (expire_at IS NULL OR expire_at > NOW()),
     *                 select the one with highest ID
     *       - Step 2: If no non-expired memberships exist, among expired memberships,
     *                 select the one with highest ID
     *       - Step 3: Uses LEFT JOIN exclusion pattern for optimal performance
     *
     * @note Performance:
     *       - More efficient than nested COALESCE subqueries
     *       - Single pass through membership table
     *       - Leverages database indexes on company_id and expire_at
     *
     * @note SQL Pattern:
     *       ```sql
     *       SELECT m1.* FROM membership as m1
     *       LEFT JOIN membership as m2 ON (conditions_that_make_m1_less_preferred)
     *       WHERE m2.id IS NULL  -- Only keep m1 records with no "better" alternative
     *       ```
     *
     * @see getEmployersByParams() Main method that uses this subquery
     * @see getEmployersById() Single employer method that uses this subquery
     *
     */
    private function prepareSubQueryLatestMembership(): QueryBuilder
    {
        return DB::table('membership as m1')
            ->select('m1.*')
            ->leftJoin('membership as m2', function($join) {
                $join->on('m1.company_id', '=', 'm2.company_id')
                    ->whereRaw('(
                        (m1.expire_at IS NULL OR m1.expire_at > NOW()) AND 
                        (m2.expire_at IS NULL OR m2.expire_at > NOW()) AND 
                        m1.id < m2.id
                    ) OR (
                        (m1.expire_at IS NOT NULL AND m1.expire_at <= NOW()) AND 
                        (m2.expire_at IS NOT NULL AND m2.expire_at <= NOW()) AND 
                        m1.id < m2.id
                    )');
            })
            ->whereNull('m2.id');
    }

    /**
     * Prepare subquery for selecting priority-based claim status for each company
     *
     * This method creates a subquery that selects the most relevant claim record
     * for each company based on a priority system. The priority ensures that
     * higher-priority claim statuses are selected over lower-priority ones,
     * with creation date as a tiebreaker.
     *
     * @return \Illuminate\Database\Query\Builder Configured query builder for claim status subquery
     *
     * @throws \Illuminate\Database\QueryException If database query fails
     *
     * @note Priority System (highest to lowest):
     *       1. "approved" (priority 1) - Company is verified and claimed
     *       2. "requested" (priority 2) - Claim request is pending review
     *       3. "rejected" (priority 3) - Claim request was denied
     *       4. Other statuses (priority 4) - Any other status values
     *
     * @note Tiebreaker Logic:
     *       - When multiple claims have the same priority status, the most recent
     *         (latest created_at) is selected
     *       - This ensures that newer claim attempts take precedence
     *
     * @note Business Logic:
     *       - An "approved" claim will always be selected over "requested" or "rejected"
     *       - If a company has both "requested" and "rejected" claims, "requested" wins
     *       - Among multiple "approved" claims, the newest one is selected
     *
     * @note SQL Implementation:
     *       - Uses CASE statement for priority ordering
     *       - Combines priority and temporal ordering in single ORDER BY
     *       - LIMIT 1 ensures only one claim per company
     *
     * @see getEmployersByParams() Main method that uses this subquery
     * @see getEmployersById() Single employer method that uses this subquery
     *
     */
    private function prepareSubQueryClaimStatus(): QueryBuilder
    {
        return DB::table('claim_companies')
            ->select('*')
            ->whereRaw('claim_companies.id = (
                SELECT cc.id FROM claim_companies cc
                WHERE cc.company_id = claim_companies.company_id
                ORDER BY
                    CASE cc.status
                        WHEN "approved" THEN 1
                        WHEN "requested" THEN 2
                        WHEN "rejected" THEN 3
                        ELSE 4
                    END ASC,
                    cc.created_at DESC
                LIMIT 1
            )');
    }
}