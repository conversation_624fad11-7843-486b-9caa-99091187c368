<?php

namespace App\Repositories;

use Illuminate\Database\Eloquent\Collection;
use App\Models\Cities;

class CitiesRepository
{
   public function getUaeEmirates(): Collection
   {
      $targetCities = [
          'Abu Dhabi Municipality',
          'Ajman', 
          'Dubai',
          'Al Fujairah Municipality',
          'Ras Al Khaimah',
          'Sharjah',
          'Umm AL Quwain'
      ];

      return Cities::query()
                ->whereIn('city_name', $targetCities)
                ->where('status', 'active')
                ->get()
                ->map(function ($city) {
                    $city->city_name = str_replace(' Municipality', '', $city->city_name);
                    return $city;
                });
   }
}