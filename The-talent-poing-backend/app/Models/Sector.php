<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Sector
 *
 * @property int $id
 * @property string $sector_name
 * @property string $status
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @method static Builder|Sector newModelQuery()
 * @method static Builder|Sector newQuery()
 * @method static Builder|Sector query()
 * @method static Builder|Sector whereCreatedAt($value)
 * @method static Builder|Sector whereId($value)
 * @method static Builder|Sector whereSectorName($value)
 * @method static Builder|Sector whereStatus($value)
 * @method static Builder|Sector whereUpdatedAt($value)
 * @mixin Eloquent
 * @mixin Builder
 * @mixin \Eloquent
 */
class Sector extends Model
{
    protected $table = 'sector';
    use HasFactory;

    protected $fillable = [
        'sector_name',
        'status',
    ];
}
