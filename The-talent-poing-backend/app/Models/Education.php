<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Education
 *
 * @property int $id
 * @property int|null $user_id
 * @property string|null $education_title
 * @property string|null $degree
 * @property string|null $start_date
 * @property string|null $end_date
 * @property string|null $currently_study_here
 * @property int|null $your_score
 * @property int|null $max_score
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Education newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Education newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Education query()
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereCurrentlyStudyHere($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereDegree($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereEducationTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereMaxScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Education whereYourScore($value)
 * @mixin \Eloquent
 */
class Education extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'education_title',
        'degree',
        'start_date',
        'end_date',
        'currently_study_here',
        'your_score',
        'max_score',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
