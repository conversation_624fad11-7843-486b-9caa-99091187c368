<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_title',
        'plan_sub_desc',
        'plan_currency',
        'plan_amount',
        'plan_type',
        'plan_points',
        'available_cv_views',
        'cv_views_is_unlimited',
        'status',
    ];

    public function advantages(): HasMany
    {
        return $this->hasMany(PackageItem::class, 'plan_id', 'id');
    }
}
