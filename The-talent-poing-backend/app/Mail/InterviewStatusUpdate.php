<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class InterviewStatusUpdate extends Mailable
{
    use Queueable, SerializesModels;

    public $interview_status; // Add this property to hold the interview status
    public $dataName;
    public $apllicantName;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($interviewStatus, $dataName, $apllicantName)
    {
        $this->interview_status = $interviewStatus;
        $this->dataName = $dataName;
        $this->apllicantName = $apllicantName;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->interview_status === 'accepted') {
            return $this->view('emails.interviewAccept')
                ->subject('Interview Accepted');
        } elseif ($this->interview_status === 'rejected') {
            return $this->view('emails.interviewReject')
                ->subject('Interview Rejected');
        } else {
            // Default behavior if the status is not recognized
            return $this->view('emails.interviewAccept')
                ->subject('Interview Changed');
        }
    }
}
