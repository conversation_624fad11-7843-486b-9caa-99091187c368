<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Membership;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ResetResumeView extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resume_view:reset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'For reset resume view monthly';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $memberships = Membership::all();
        $today = Carbon::now();

        foreach ($memberships as $membership) {
            $plan_id = (int) $membership->plan_id;

            $user = User::find($membership->user_id);
            $company = Company::find($membership->company_id);

            if (Carbon::parse($membership->expire_at)->lt($today)) {
                $firstPlan = Plan::orderBy('id', 'asc')->first();

                $available_resume_count_base = 0;

                if ($firstPlan->available_cv_views)
                {
                    $available_resume_count_base = $firstPlan->available_cv_views;
                }

                $this->updateResumeView($user, $company, $available_resume_count_base);
            } else {
                $plan = Plan::find($plan_id);

                $available_resume_count = 0;

                if ($plan->available_cv_views)
                {
                    $available_resume_count = $plan->available_cv_views;
                }

                $this->updateResumeView($user, $company, $available_resume_count);
            }
        }

        return Command::SUCCESS;
    }

    private function updateResumeView($user, $company, $count)
    {
        if (isset($user, $user->available_resume_count))
        {
            if ($user->role == 'staff')
            {
                $user->available_resume_count = $count;
                $user->save();
            }
        }

        if (isset($company, $company->available_resume_count))
        {
            $company->available_resume_count = $count;
            $company->save();
        }
    }
}
