<?php

namespace App\Http\Controllers\Api;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;
use App\Models\Skills;
use App\Models\Job;
use App\Classes\ErrorsClass;
//use JWTAuth;
use DB;

class SkillsController extends Controller
{
    use ApiResponseHelpers;

    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function getAllSkills()
    {
        try {
            $skills = Skills::where('status', 'active')->orderBy('id', 'desc')->get();
            return response()->json(['success' => true, 'data' => $skills], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function searchSkills(Request $request)
    {
        try {
            $query = $request->input('query');
            $skills = Skills::where('skills', 'LIKE', $query . '%')
                ->where('status', 'active')
                ->orderBy('id', 'desc')
                ->get();

            return response()->json(['success' => true, 'data' => $skills], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function searchAllskill(Request $request)
    {
        try {
            $query = DB::table('skills');
            if ($request->keywords) {
                $query->where('skills', 'like', '%' . $request->keywords . '%');
            }
            if ($request->order_by) {
                if ($request->order_by == 'asc') {
                    $query->orderBy('id', 'ASC');
                }
                $query->orderBy('id', 'DESC');
            }
            $query->where('status', '!=', 'deleted');
            $skill = $query->get();
            if ($skill) {
                return response()->json(['status' => true, 'message' => "search skill data fetch successfully", 'data' => $skill], 200);
            } else {
                return response()->json(['status' => false, 'message' => "No search skill data found", 'data' => ""], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }



    public function getSingleSkills($id)
    {
        try {
            $skill = Skills::findOrFail($id);
            return response()->json(['success' => true, 'data' => $skill], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
    public function getJobsSkills(Request $request)
    {
        try {


            $id = $request->job_id;

            // $jobs = Job::select('id')->where('job_slug',$id)->first();
            $jobs = Job::select('id')->where('id', $id)->first();

            if (!$jobs)
            {
                return response()->json([
                    'status' => false,
                    'message' => 'Job not found',
                    'data' => [],
                ], 200);
            }

//            $jobs_data = Job::findOrFail($jobs->id);
            $jobs_data = Job::find($jobs->id);

            if (!$jobs_data)
            {
                return response()->json([
                    'status' => false,
                    'message' => 'Job data not found',
                    'data' => [],
                ], 200);
            }

            $skills = Skills::whereIn('id', explode(',', $jobs_data->skills_required))->get();
            return response()->json([
                'status' => true,
                'message' => 'skills retrieved successfully',
                'data' => $skills,
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllSkillsForAdmin()
    {
        try {

            $skills = Skills::select('sector_name', 'sector.id as sector_id', 'skills', 'skills.id')->join('sector', 'skills.sector_id', '=', 'sector.id')->where('skills.status', 'active')->orderBy('skills.id', 'desc')->get();

            return response()->json(['success' => true, 'data' => $skills], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function editAndSaveSkill(Request $request)
    {
        try {

            if ($request->id) {

                $skill = Skills::findOrFail($request->id);
                $skill->skills = $request->skills;
                $skill->sector_id = $request->sector_id;
                $skill->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Skill updated successfully',
                ], 200);
            } else {

                $skill = new Skills;
                $skill->skills = $request->skills;
                $skill->sector_id = $request->sector_id;
                $skill->status = $request->status ?? 'active';
                $skill->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Skill added successfully',
                ], 200);
            }
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteSkill(Request $request)
    {
        try {

            $sector = Skills::find($request->id);
            $sector->status = 'deleted';
            $sector->save();

            return response()->json([
                'status' => true,
                'message' => 'Skill has been deleted successfully!',
            ], 200);
        } catch (\Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getSingleSkillByName(Request $request): JsonResponse
    {
        try {
            $skillName = $request->get('skill');
            $skill_data = (new Skills)->where('skills', 'like', '%' . $skillName . '%')->where('status', 'active');
            if ($skill_data) {
                return $this->respondWithSuccess($skill_data->first());
            } else {
                return $this->respondNoContent();
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }
}
