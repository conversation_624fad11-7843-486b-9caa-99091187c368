<?php

namespace App\Http\Controllers\Api;

use Exception;
use F9Web\ApiResponseHelpers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Infrastructure\Resources\JobResource;
use Src\JobsManagement\Application\FindJobService;
use Src\JobsManagement\Application\JobSearchService;
use Src\JobsManagement\Application\ToggleSaveJobService;
use Src\JobsManagement\Infrastructure\Resources\JobSearchResource;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use App\Models\Job;
use App\Models\SavedJobs;
use App\Models\Applications;
use App\Models\UserSearchJobs;
use App\Models\JobsView;
use App\Models\Sector;
use App\Models\JobCountryFaqs;
use App\Models\Country;
use App\Models\Cities;
use App\Models\User;
use Carbon\Carbon;
use App\Models\Company;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Models\JobFilter;
use App\Models\CompanyFollower;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class JobsController extends ApiController
{
    use ApiResponseHelpers;
    public function index(Request $request)
    {

        $keywords = $request->get('keywords');
        if ($request->get('country')) {
            $country = $request->get('country');
        } else {
            $country = $request->get('location');
        }
        $city = $request->get('city');
        $salary = $request->get('salary');
        $currency = $request->get('currency');
        $experience = $request->get('experience');
        $sortby = $request->get('sortby');
        $job_type = $request->get('job_type');
        $sector = $request->get('sector');
        $skill = $request->get('skill');
        $job_id = $request->get('job_id');
        $faqLocation = $request->get('faqLocation');
        $page_size = $request->get('pageSize');
        $postal_code = $request->get('postal_code');
        $street_address = $request->get('street_address');

        try {
            if (request()->bearerToken() && $user = Auth::guard('sanctum')->user()) {
                Auth::setUser($user);
            }
            $user = (new User())->find(Auth::id());

            if (!empty($keywords) && is_string($keywords)) {
                $UserSearchJobs = UserSearchJobs::updateOrCreate([
                    'sector_name' => $keywords,
                    'country_name' => $country,
                ], [
                    'sector_id' => Sector::firstWhere('sector_name', $keywords)?->id ?? null,
                    'country_id' => Country::firstWhere('country_name', $country)?->id ?? null
                ]);
                $UserSearchJobs->updated_at = date('Y-m-d H:i:s');

                $UserSearchJobs->save();
            }

            if ($job_id)
            {
                $checkedJob = Job::find($job_id);

                if ($checkedJob)
                {
                    $jobsView = new JobsView();
                    $jobsView->job_id = $job_id;

                    if (Auth::check()) {
                        $jobsView->user_id = Auth::id();
                    }

                    $jobsView->save();
                }
            }

            return (new JobSearchService)($user, $postal_code, $street_address, $keywords, $country, $city, $salary, $currency, $sector, $experience, $sortby, $job_type, $job_id, $faqLocation, $page_size);
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getAllJobsList(Request $request)
    {
        $jobs = Job::query()->with(['applications', 'views'])
            ->where('job_status', 'active')
            ->whereHas('company')
            ->whereHas('user')
            ->withCount([
                'views',
                'applications',
            ]);

        if ($request->company_slug)
        {
            $company = Company::where('company_slug', $request->company_slug)->first();

            if ($company)
            {
                $jobs->where('company_id', $company->id);
            }
        }

        if ($request->company)
        {
            if (is_array($request->company))
            {
                $jobs->whereIn('company_id', $request->company);
            } else {
                $jobs->where('company_id', $request->company);
            }
        }

        if ($request->search)
        {
            $jobs->where('job_title', 'LIKE', '%' . $request->search . '%');
        }

        if ($request->job_title)
        {
            if (is_array($request->job_title))
            {
                $jobs->where('job_title', 'LIKE', '%' . $request->job_title[0] . '%');
            } else {
                $jobs->where('job_title', 'LIKE', '%' . $request->job_title . '%');
            }
        }

        if ($request->sector)
        {
            if (is_array($request->sector))
            {
                $jobs->whereIn('sector_id', $request->sector);
            } else {
                $jobs->where('sector_id', $request->sector);
            }
        }

        if ($request->location)
        {
            if (is_array($request->location))
            {
                $jobs->whereIn('job_city', $request->location);
            } else {
                $jobs->where('job_city', $request->location);
            }
        }

        if ($request->expired_job)
        {
            $expired_range = $request->expired_job;

            if ($expired_range)
            {
                $expired_range = $expired_range[0];
            }

            $today = Carbon::today();

            if (str_contains($expired_range, ' - ')) {
                $rangeParts = array_map('trim', explode(' - ', $expired_range));
                $dateFrom = $rangeParts[0] ?? null;
                $dateTo = $rangeParts[1] ?? null;

                $minDateOfExpired = Carbon::parse($dateFrom);
                $maxDateOfExpired = Carbon::parse($dateTo);
                $jobs->whereBetween('deadline', [$minDateOfExpired, $maxDateOfExpired]);
            } elseif (str_contains($expired_range, 'From: ')) {
                $dateFrom = str_replace('From: ', '', $expired_range);
                $maxDateOfExpired = Carbon::parse(trim($dateFrom));
                $jobs->where('deadline', '>=', $maxDateOfExpired);
            } elseif (str_contains($expired_range, 'To: ')) {
                $dateTo = str_replace('To: ', '', $expired_range);
                $minDateOfExpired = Carbon::parse(trim($dateTo));
                $jobs->where('deadline', '<=', $minDateOfExpired);
            }
        }

        $allowedJobStatusList = [
            'Active',
            'Expired'
        ];

        if ($request->job_status)
        {
//            if (in_array($request->job_status, $allowedJobStatusList))
//            {
//                $jobs->where('job_status', $request->job_status);
//            } else {
//                $jobs->whereIn('job_status', $allowedJobStatusList);
//            }

            $today = Carbon::today();

            switch ($request->job_status)
            {
                case 'Active':
                    $jobs->where('deadline', '>=', $today);
                    break;
                case 'Expired':
                    $jobs->where('deadline', '<', $today);
                    break;
            }
        }

        if ($request->impressions)
        {
            if (is_array($request->impressions))
            {
                switch ($request->impressions[0])
                {
                    case 'low_to_high':
                        $jobs->orderBy('views_count', 'asc');
                        break;
                    case 'high_to_low':
                        $jobs->orderBy('views_count', 'desc');
                        break;
                }
            }
        }

        if ($request->responses)
        {
            if (is_array($request->responses))
            {
                switch ($request->responses[0])
                {
                    case 'low_to_high':
                        $jobs->orderBy('applications_count', 'asc');
                        break;
                    case 'high_to_low':
                        $jobs->orderBy('applications_count', 'desc');
                        break;
                }
            }
        }

        switch ($request->sort) {
            case 'Recent':
                $jobs->orderBy('created_at', 'desc');
                break;
            case 'Old':
                $jobs->orderBy('created_at', 'asc');
                break;
            case 'Featured: Recent':
                $jobs->orderByRaw("
                    is_featured DESC,
                    CASE WHEN is_featured = 1 THEN created_at ELSE NULL END DESC,
                    CASE WHEN is_featured = 0 THEN created_at ELSE NULL END DESC
                ");
                break;
            case 'Featured: Old to new':
                $jobs->orderByRaw("
                    is_featured DESC,
                    CASE WHEN is_featured = 1 THEN created_at ELSE NULL END ASC,
                    CASE WHEN is_featured = 0 THEN created_at ELSE NULL END ASC
                ");
                break;
            default:
                $jobs->orderBy('created_at', 'desc');
                break;
        }

        $pageSize = $request->get('per_page', 10);
        $jobsPaginated = $jobs->paginate($pageSize, ['*'], 'page', $request->get('page', 1));;

        return JobResource::collection($jobsPaginated);
    }

    public function getAllJobs()
    {
        try {
            $jobs = Job::select('jobs.*', 'countries.country_name as job_country_name', 'company.company_logo', 'company.company_slug')
                ->leftJoin('countries', 'jobs.job_country', '=', 'countries.id')
                ->leftjoin('company', 'jobs.company_id', '=', 'company.id')
                ->where('jobs.job_status', '=', 'active')
                //->where('applications.hiring_status', '=', 'Maybe')
                ->orderByDesc('jobs.id')
                ->get();
            $totalshortlistedjobs = Job::select('jobs.*', 'countries.country_name as job_country_name')
                ->leftJoin('countries', 'jobs.job_country', '=', 'countries.id')
                ->where('jobs.job_status', '=', 'active')
                ->get();
            return response()->json([
                'status' => 200,
                'message' => 'All jobs retrieved successfully',
                'data' => $jobs,
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getAllJobsWithId($id)
    {
        try {
            $country_id = User::where('id', $id)->pluck('where_job_search')->first();
            $jobs = Job::with('country', 'company', 'company.logo')
                ->where('job_status', 'active')
                ->whereHas('company', function ($query) {
                    $query->whereHas('user');
                })
                ->active()
                ->country($country_id)
                ->latest()
                ->limit(3)
                ->get();

            if ($jobs) {
                $jobs = Job::with('country', 'company', 'company.logo')
                    ->where('job_status', 'active')
                    ->whereHas('company', function ($query) {
                        $query->whereHas('user');
                    })
                    ->active()
                    ->latest()
                    ->limit(3)
                    ->get();
            }

            $jobs = JobSearchResource::collection($jobs);

            foreach ($jobs as $job) {
                $app = Applications::where(['user_id' => $id, 'job_id' => $job->id])->first();
                $savedJob = SavedJobs::where(['user_id' => $id, 'job_id' => $job->id])->first();
                if ($app) {
                    $job->is_appied = true;
                }
                if ($savedJob) {
                    $job->is_saved = true;
                }
            }

            return response()->json([
                'status' => 200,
                'message' => 'All jobs retrieved successfully',
                'data' => $jobs,
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getCurrentUserAllJobs(Request $request)
    {
        try {
            $company_id = $request->company_id;
            $job_sort = $request->job_sort;
            if ($job_sort) {
                $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_slug', 'users.name as job_created_name', 'users.id as job_created_by_id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('users', 'jobs.user_id', '=', 'users.id')
                    ->where('jobs.company_id', $company_id)
                    ->where('jobs.job_status', '=', $job_sort)
                    ->orderByDesc('id')
                    ->get();
                foreach ($jobs as $jobs_data) {
                    $jobs_id = $jobs_data->id;
                    $jobs_shortlisted_count = Applications::where(['job_id' => $jobs_id, 'hiring_status' => 'Yes'])->count();
                    $jobs_rejected_count = Applications::where(['job_id' => $jobs_id, 'hiring_status' => 'No'])->count();
                    $jobs_applicants_count = Applications::where(['job_id' => $jobs_id])->count();
                    $jobs_views_count = JobsView::where('job_id', $jobs_id)->count();
                    $currentDate = Carbon::now();
                    $lastWeekStartDate = $currentDate->subWeek();
                    $lastWeekEndDate = Carbon::now();
                    $jobViewCountslastweek = JobsView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();
                    $last_Week_Start_Date = Carbon::now()->subWeek();
                    $lastTwoWeekStartDate = $currentDate->subWeek(1);
                    $lastTwoWeekEndDate = $last_Week_Start_Date;
                    //$weekBeforeStart = now()->startOfWeek()->subWeeks(1);
                    //$weekBeforeEnd = now()->endOfWeek()->subWeeks(1);
                    $jobViewCountslasttwoweek = JobsView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();

                    $Impressions_First_Week = $jobViewCountslasttwoweek;
                    $Impressions_Last_Week = $jobViewCountslastweek;
                    if ($Impressions_First_Week > 0 && $Impressions_Last_Week > 0) {
                        $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                        $impression_percentage = $impression_count . '%';
                    } else {
                        $impression_percentage = '0%';
                    }
                    $jobs_data['shortlisted_jobs_count'] = $jobs_shortlisted_count;
                    $jobs_data['rejected_jobs_count'] = $jobs_rejected_count;
                    $jobs_data['jobs_view_count'] = $jobs_views_count;
                    $jobs_data['jobs_applicants'] = $jobs_applicants_count;
                    $jobs_data['jobViewCountslastweek'] = $jobViewCountslastweek;
                    $jobs_data['jobViewCountslasttwoweek'] = $jobViewCountslasttwoweek;
                    $jobs_data['jobsImpressionpercentage'] = $impression_percentage;
                }
                return response()->json([
                    'status' => true,
                    'message' => 'All jobs retrieved successfully',
                    'data' => $jobs,
                ], 200);
            } else {
                $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_slug', 'users.name as job_created_name', 'users.id as job_created_by_id')
                    ->join('countries', 'jobs.job_country', '=', 'countries.id')
                    ->join('company', 'jobs.company_id', '=', 'company.id')
                    ->join('users', 'jobs.user_id', '=', 'users.id')
                    ->where('jobs.company_id', $company_id)
                    ->where('jobs.job_status', '!=', 'deleted')
                    ->orderByDesc('id')
                    ->get();
                foreach ($jobs as $jobs_data) {
                    $jobs_id = $jobs_data->id;
                    $jobs_shortlisted_count = Applications::where(['job_id' => $jobs_id, 'hiring_status' => 'Yes'])->count();
                    $jobs_rejected_count = Applications::where(['job_id' => $jobs_id, 'hiring_status' => 'No'])->count();
                    $jobs_applicants_count = Applications::where(['job_id' => $jobs_id])->count();
                    // $jobs_views_count = JobsView::where('job_id', $jobs_id)->count();
                    $jobs_views_count = JobsView::where('job_id', $jobs_id)->distinct()->count('user_id');

                    $currentDate = Carbon::now();
                    $lastWeekStartDate = $currentDate->subWeek();
                    $lastWeekEndDate = Carbon::now();
                    $jobViewCountslastweek = JobsView::whereBetween('created_at', [$lastWeekStartDate, $lastWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();
                    $last_Week_Start_Date = Carbon::now()->subWeek();
                    $lastTwoWeekStartDate = $currentDate->subWeek(1);
                    $lastTwoWeekEndDate = $last_Week_Start_Date;
                    //$weekBeforeStart = now()->startOfWeek()->subWeeks(1);
                    //$weekBeforeEnd = now()->endOfWeek()->subWeeks(1);
                    $jobViewCountslasttwoweek = JobsView::whereBetween('created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                        //->selectRaw('date(created_at) as date, count(*) as count')
                        ->where('job_id', $jobs_id)
                        ->where('status', 'active')
                        //->groupBy('date')
                        ->count();

                    $Impressions_First_Week = $jobViewCountslasttwoweek;
                    $Impressions_Last_Week = $jobViewCountslastweek;

                    if ($Impressions_First_Week > 0 && $Impressions_Last_Week > 0) {
                        $impression_count = (($Impressions_First_Week - $Impressions_Last_Week) / $Impressions_Last_Week) * 100;
                        $impression_percentage = $impression_count;
                    } else {
                        $impression_percentage = '0';
                    }

                    $totaljobimpressionn = $jobViewCountslasttwoweek + $jobViewCountslastweek;

                    $jobs_data['shortlisted_jobs_count'] = $jobs_shortlisted_count;
                    $jobs_data['rejected_jobs_count'] = $jobs_rejected_count;
                    $jobs_data['jobs_view_count'] = $jobs_views_count;
                    $jobs_data['jobs_applicants'] = $jobs_applicants_count;
                    $jobs_data['jobViewCountslastweek'] = $jobViewCountslastweek;
                    $jobs_data['jobViewCountslasttwoweek'] = $jobViewCountslasttwoweek;
                    $jobs_data['jobsImpressionpercentage'] = $impression_percentage;
                    $jobs_data['totaljobimpression'] = $totaljobimpressionn;
                }
                return response()->json([
                    'status' => true,
                    'message' => 'All jobs retrieved successfully',
                    'data' => $jobs,
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getCompanyActiveJobs(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $company_id = $request->company_id;
            if (is_numeric($company_id)) {
                $company = Company::select('id')->where('id', $company_id)->first();
            } else {
                $company = Company::select('id')->where('company_slug', $company_id)->first();
            }

            $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->where('jobs.company_id', $company->id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->get();

            $featuredjobscount = Job::where('jobs.company_id', $company->id)->where('jobs.job_status', '=', 'active')->where('jobs.is_featured', '=', '1')->count();

            foreach ($jobs as $job) {
                $app = Applications::where(['user_id' => $user_id, 'job_id' => $job->id])->where('status', '!=', 'deleted')->first();
                if ($app) {
                    $job->is_appied = true;
                }
            }

            $currentDate = Carbon::now();
            $lastWeekStartDate = $currentDate->subWeek();
            $lastWeekEndDate = Carbon::now();
            $jobslastweek = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->whereBetween('jobs.created_at', [$lastWeekStartDate, $lastWeekEndDate])
                ->where('jobs.company_id', $company->id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->count();
            $last_Week_Start_Date = Carbon::now()->subWeek();
            $lastTwoWeekStartDate = $currentDate->subWeek(1);
            $lastTwoWeekEndDate = $last_Week_Start_Date;
            $jobslasttwoweek = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id')
                ->join('countries', 'jobs.job_country', '=', 'countries.id')
                ->join('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->whereBetween('jobs.created_at', [$lastTwoWeekStartDate, $lastTwoWeekEndDate])
                ->where('jobs.company_id', $company->id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->count();
            $Jobs_Impressions_First_Week = $jobslasttwoweek;
            $Jobs_Impressions_Last_Week = $jobslastweek;
            if ($Jobs_Impressions_First_Week > 0 && $Jobs_Impressions_Last_Week > 0) {
                $jobs_impression_count = (($Jobs_Impressions_First_Week - $Jobs_Impressions_Last_Week) / $Jobs_Impressions_Last_Week) * 100;
                $jobsimpressionpercentage = number_format($jobs_impression_count, 2) . '%';
            } else {
                $jobsimpressionpercentage = '0%';
            }

            $plan_id = 1;

            $membership = $company->membership;

            if ($membership && $membership->plan_id)
            {
                $plan_id = $membership->plan_id;
            }

            $jobs_lasttwoweek = $jobslasttwoweek;
            $jobs_lastweek = $jobslastweek;
            $jobs_Impression_percentage = $jobsimpressionpercentage;
            return response()->json([
                'status' => true,
                'message' => 'All jobs retrieved successfully',
                'data' => $jobs,
                'jobs_Impression_percentage' => $jobs_Impression_percentage,
                'jobs_lasttwoweek' => $jobs_lasttwoweek,
                'jobs_lastweek' => $jobs_lastweek,
                'featuredjobscount' => $featuredjobscount,
                'plan_id' => $plan_id
            ], 200);
        } catch (Exception $exception) {
            return response()->json([
                'error' => [
                    'message' => $exception->getMessage(),
                    // 'code' => $exception->getStatusCode(),
                    // 'details' => $exception->getTraceAsString() // You can omit this in production
                ]
            ], 500);
        }
    }

    public function getSingleJob($id): JsonResponse
    {

        try {
            $job = (new Job)->with('country', 'company', 'company.logo', 'city', 'company.jobs', 'banner')->find($id);

            if (!$job->company)
            {
                return response()->json([
                    'message' => 'Job was deleted',
                ], 410);
            }

            if ($job) {
                return $this->respondWithSuccess(new JobResource($job));
            }

            return response()->json([
                'message' => 'No Jobs Found',
            ], 404);
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getSortByJobs($option)
    {
        try {
            if ($option) {
                $jobs = Job::where('job_status', '=', $option)
                    ->orderByDesc('id')
                    ->get();
            } else {
                $jobs = Job::where('job_status', '!=', 'deleted')
                    ->orderByDesc('id')
                    ->get();
            }
            return response()->json([
                'status' => 200,
                'message' => 'Jobs retrieved successfully',
                'data' => $jobs,
            ], 200);
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    function createJob(Request $request): JsonResponse
    {
        try {
            $authUser = (new User)->find(Auth::id());
            $country = Country::select('country_name')->where('id', $request->get('country_id'))->first();
            $cities = Cities::select('city_name')->where('id', $request->job_city)->first();

            if ($authUser->role === 'admin' || $request->has('company_id')) {
                $company = Company::select('user_id', 'company_name', 'company_slug', 'company_website', 'company_location')
                    ->where('id', $request->get('company_id'))
                    ->first();
            } else {
                $company = $authUser->company;
            }

            if (!$company)
            {
                return response()->json([
                    'status' => false,
                    'error' => 'Selected company no longer exists'
                ], 410);
            }

            $meta_tag = "{$request->title} | Jobs in {$country->country_name}, {$cities->city_name}, by {$company->company_name} | " . str_replace('https://', '', $company->company_website);
            $meta_desc = "{$request->title} vacancy in {$country->country_name}, {$cities->city_name} with {$company->company_name}. Search for more {$request->job_title} jobs in {$country->country_name}, {$cities->city_name}.";

            $jobs = $request->has('job_id') ? Job::findOrFail($request->get('job_id')) : new Job;
            $jobs->user_id = $request->user_id;
            $jobs->company_id = $request->company_id;
            $jobs->job_title = $request->get('title');
            $jobs->job_description = $request->get('description');
            $jobs->job_type = $request->get('type');
            $jobs->job_country = $request->get('country_id');
            $jobs->job_city = $request->job_city;
            $jobs->industry = $request->industry;
            $jobs->sector_id = $request->sector_id;
            $jobs->experience = $request->experience;
            $jobs->skills_required = implode(',', $request->skills);
            $jobs->monthly_fixed_salary_currency = $request->monthly_fixed_salary_currency;
            $jobs->monthly_fixed_salary_min = $request->monthly_fixed_min_salary;
            $jobs->monthly_fixed_salary_max = $request->monthly_fixed_max_salary;
            $jobs->available_vacancies = $request->available_vacancies;
            $jobs->postal_code = $request->postal_code;
            $jobs->street_address = $request->street_address;
            if ($request->has('deadline')) {
                $jobs->deadline = Carbon::parse($request->get('deadline'));
            }
            $jobs->is_featured = $request->get('featured');
            $jobs->hide_employer_details = $request->get('hide_employer_details', false);
            $jobs->meta_tag = $meta_tag;
            $jobs->meta_desc = $meta_desc;
            $jobs->job_status = $request->get('job_status', 'active');

            if ($jobs->save()) {
                $jobslugupdate = Job::findOrFail($jobs->id);
                $jobslugupdate->job_slug = Str::slug($cities->city_name, '-') . '/' . Str::slug($request->title, '-') . '-' . $jobs->id;


                $jobslugupdate->save();

                $companyfollowersuserdata = CompanyFollower::Select('name', 'email')->join('users', 'company_followers.user_id', '=', 'users.id')
                    ->where('company_followers.company_id', $request->company_id)
                    ->where('company_followers.status', 'active')->get();

                foreach ($companyfollowersuserdata as $user) {

                    // Mail::to($user->email)->send(new CompanyFollowerJobMail($user->name, $request->title, $company->company_name, $request->experience, $country->country_name, $jobslugupdate->job_slug, $company->company_slug));

                }


                return response()->json(['status' => true, 'message' => 'Jobs created successfully', 'data' => $jobslugupdate]);
            } else {
                return response()->json(['status' => false, 'message' => 'Jobs not created successfully', 'data' => '']);
            }
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    function updateJob(Request $request, $id)
    {
        try {


            $country = Country::select('country_name')->where('id', $request->edit_job_country)->first();
            $cities = Cities::select('city_name')->where('id', $request->edit_job_city)->first();
            $company = Company::select('company_name', 'company_slug', 'company_website', 'company_location')->where('id', $request->edit_company_id)->first();


            $meta_tag = "{$request->job_title} | Jobs in {$country->country_name}, {$cities->city_name} by {$company->company_name} | " . str_replace('https://', '', $company->company_website);

            $meta_desc = "{$request->job_title} vacancy in {$country->country_name}, {$cities->city_name} with {$company->company_name}. Search for more {$request->job_title} jobs in {$country->country_name}, {$cities->city_name}.";


            $jobs = Job::find($id);
            $jobs->user_id = $request->edit_user_id;
            $jobs->company_id = $request->edit_company_id;
            $jobs->sector_id = $request->edit_sector;
            $jobs->job_title = $request->edit_job_title;
            $jobs->job_description = $request->edit_job_description;
            //$jobs->type_of_position = $request->edit_type_of_position;
            $jobs->type_of_position = null;
            $jobs->job_type = $request->edit_job_type;
            $jobs->job_country = $request->edit_job_country;
            $jobs->job_city = $request->edit_job_city;
            $jobs->industry = $request->edit_industry;
            $jobs->experience = $request->edit_experience;
            $jobs->skills_required = implode(',', $request->edit_skills);
            $jobs->monthly_fixed_salary_currency = $request->edit_monthly_fixed_salary_currency;
            $jobs->monthly_fixed_salary_min = $request->edit_monthly_fixed_min_salary;
            $jobs->monthly_fixed_salary_max = $request->edit_monthly_fixed_max_salary;
            $jobs->available_vacancies = $request->edit_available_vacancies;
            $jobs->deadline = $request->edit_deadline;
            $jobs->is_featured = $request->edit_is_featured;
            $jobs->meta_tag = $meta_tag;
            $jobs->meta_desc = $meta_desc;
            $jobs->job_slug = Str::slug($cities->city_name, '-') . '/' . Str::slug($request->edit_job_title, '-') . '-' . $id;

            if ($request->edit_hide_employer_details == 'on') {
                $jobs->hide_employer_details = '1';
            } else {
                $jobs->hide_employer_details = '0';
            }
            $jobs->job_status = $request->edit_close_job;
            $jobs->save();

            return response()->json(['message' => 'Jobs updated successfully', 'status' => true], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function deleteJob($id)
    {
        try {
            $jobs = Job::find($id);
            $jobs->job_status = 'deleted';
            $applicationUserIds = DB::table('applications')->where('job_id', $id)->pluck('user_id');
            $jobs->save();

            foreach ($applicationUserIds as $userId) {
                $user = User::find($userId);
                $message = 'Dear ' . $user->name . ', Your job application has been deleted due to this job is no longer available.';
                \Mail::raw($message, function ($message) use ($user) {
                    $message->to($user->email)->subject('Job Application Deleted');
                });
            }

            return response()->json(['message' => 'Job deleted successfully', 'status' => true]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserSavedJobs(Request $request)
    {
        try {
            $user_id = $request->user_id;

            $saved_jobs = Job::with('country', 'company', 'company.logo')
                ->active()
                ->orderByDesc('jobs.id')
                ->savedJob($user_id)
                ->select('jobs.*')
                ->get();
            $saved_jobs = JobSearchResource::collection($saved_jobs);


            foreach ($saved_jobs as $job) {
                $app = Applications::where(['user_id' => $user_id, 'job_id' => $job->id])->first();
                if ($app) {
                    $job->is_applied = true;
                } else {
                    $job->is_applied = false;
                }
                $job->is_saved = true;
            }

            return response()->json(['status' => true, 'saved_jobs' => $saved_jobs]);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function toggleFavorite(Request $request, $id): JsonResponse
    {
        try {
            $user = (new User)->find(Auth::id());
            $job = (new FindJobService)($id);
            $saved = (new ToggleSaveJobService)($user, $job);
            return $this->respondWithSuccess($saved);
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function deleteUnSavedJob(Request $request, $id)
    {
        try {
            $saved_jobs = SavedJobs::where('id', $id)->first();

            if (!$saved_jobs) {
                return response()->json(['status' => false, 'message' => 'Saved Job not found']);
            }
            $delete_saved_jobs = SavedJobs::where('id', $id)->delete();

            return response()->json(['status' => true, 'message' => ' Job UnSaved successfully']);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    // public function getAllJobsSearch(Request $request)
    // {
    //     try {
    //         $id = $request->user_id;

    //         /*$query = Job::select('jobs.*', 'countries.country_name as job_country_name', 'saved_jobs.id as saved_id', 'company.company_name', 'company.company_logo', 'company.company_slug')
    //             ->leftJoin('countries', 'jobs.job_country', '=', 'countries.id')
    //             ->leftJoin('company', 'jobs.company_id', '=', 'company.id')
    //             ->leftJoin('saved_jobs', function ($join) use ($id) {
    //                 $join->on('jobs.id', '=', 'saved_jobs.job_id')
    //                     ->where('saved_jobs.user_id', '=', $id);
    //             });*/

    //         $query = (new Job)
    //             ->with('country', 'company.logo')
    //             ->active()
    //             ->leftJoin('company', 'jobs.company_id', '=', 'company.id');

    //         if ($request->keywords) {
    //             $query->where(function ($q) use ($request) {
    //                 $q->where('jobs.job_title', 'LIKE', '%' . $request->keywords . '%')
    //                     ->orWhere('company.company_name', 'LIKE', '%' . $request->keywords . '%');
    //             });
    //         }

    //         if ($request->location) {
    //             $locations_ids = explode(',', $request->location);
    //             $query->whereIn('jobs.job_country', $locations_ids);
    //         }

    //         if ($request->city) {
    //             if (preg_match('~[0-9]+~', $request->city)) {
    //                 $city_ids = explode(',', $request->city);
    //                 $query->whereIn('jobs.job_city', $city_ids);
    //             } else {
    //                 $city_data = DB::table('cities')->where('city_name', 'like', '%' . $request->city . '%')->where('status', 'active')->first();
    //                 $city_ids = explode(',', $city_data->id);
    //                 $query->whereIn('jobs.job_city', $city_ids);
    //             }
    //         }

    //         if ($request->experience) {
    //             $query->where('jobs.experience', $request->experience);
    //         }

    //         if (isset($request->currency)) {
    //             $minSalary = (int) $request->minsalary; // Convert minsalary to integer
    //             $query->where(function ($query) use ($minSalary) {
    //                 $query->where('monthly_fixed_salary_min', '>=', $minSalary);
    //             });
    //         }

    //         if ($request->skill) {
    //             if (preg_match('~[0-9]+~', $request->skill)) {
    //                 $skill_arr = explode(',', $request->skill);
    //                 $query->where(function ($query) use ($skill_arr) {
    //                     foreach ($skill_arr as $skill_id) {
    //                         $query->Where('jobs.skills_required', 'LIKE', "%,$skill_id,%");
    //                     }
    //                 });
    //             } else {
    //                 $skill_data = DB::table('skills')->where('skills', 'like', '%' . $request->skill . '%')->where('status', 'active')->first();
    //                 $skill_arr = explode(',', $skill_data->id);
    //                 $query->where(function ($query) use ($skill_arr) {
    //                     foreach ($skill_arr as $skill_id) {
    //                         $query->Where('jobs.skills_required', $skill_id);
    //                     }
    //                 });
    //             }
    //         }

    //         if ($request->sector) {
    //             $sector_arr = array_map('intval', explode(',', $request->sector));
    //             $query->whereIn('jobs.sector_id', $sector_arr);
    //         }

    //         if ($request->job_type) {
    //             $job_type_arr = explode(',', $request->job_type);
    //             $query->where(function ($query) use ($job_type_arr) {
    //                 foreach ($job_type_arr as $job_type) {
    //                     $query->orWhere('jobs.job_type', 'LIKE', "%,$job_type,%")
    //                         ->orWhere('jobs.job_type', 'LIKE', "$job_type,%")
    //                         ->orWhere('jobs.job_type', 'LIKE', "%,$job_type")
    //                         ->orWhere('jobs.job_type', 'LIKE', "%$job_type%");
    //                 }
    //             });
    //         }

    //         if (isset($request->currency)) {
    //             $currency = $request->currency; // Get the currency from the request
    //             $minSalary = (int) $request->salary; // Convert minsalary to integer

    //             $query->where(function ($query) use ($currency, $minSalary) {
    //                 $query->where('jobs.monthly_fixed_salary_currency', $currency)
    //                     ->where('jobs.monthly_fixed_salary_min', '>=', $minSalary);
    //             });
    //         }

    //         $query->where('jobs.job_status', '=', 'active');
    //         $query->orderByDesc('jobs.id');
    //         $jobs = $query->paginate();

    //         foreach ($jobs as $job) {
    //             $app = Applications::where(['user_id' => $id, 'job_id' => $job->id])->first();
    //             if ($app) {
    //                 $job->is_applied = true;
    //             } else {
    //                 $job->is_applied = false;
    //             }
    //         }

    //         return JobSearchResource::collection($jobs);
    //     } catch (Exception $e) {
    //         return $this->respondError($e->getMessage());
    //     }
    // }


    public function getAllJobsSearch(Request $request)
    {
        try {
            $id = $request->user_id;

            $query = (new Job)
                ->with('country', 'company.logo')
                ->active()
                ->leftJoin('company', 'jobs.company_id', '=', 'company.id')
                ->select('jobs.*', 'jobs.id as job_id', 'company.id as company_id', 'company.company_name', 'company.user_id as user_id');

//            $query = (new Job)
//                ->with('country', 'company.logo')
//                ->active()
//                ->leftJoin('company', 'jobs.company_id', '=', 'company.id');

            // NEW: Add filter for company_id
            if ($request->has('company_id')) {
                $query->where('jobs.company_id', $request->company_id);
            }

            if ($request->keywords) {
                $query->where(function ($q) use ($request) {
                    $q->where('jobs.job_title', 'LIKE', '%' . $request->keywords . '%')
                        ->orWhere('company.company_name', 'LIKE', '%' . $request->keywords . '%');
                });
            }

            if ($request->location) {
                $locations_ids = explode(',', $request->location);
                $query->whereIn('jobs.job_country', $locations_ids);
            }

            if ($request->city) {
                if (preg_match('~[0-9]+~', $request->city)) {
                    $city_ids = explode(',', $request->city);
                    $query->whereIn('jobs.job_city', $city_ids);
                } else {
                    $city_data = DB::table('cities')->where('city_name', 'like', '%' . $request->city . '%')->where('status', 'active')->first();
                    $city_ids = explode(',', $city_data->id);
                    $query->whereIn('jobs.job_city', $city_ids);
                }
            }

            if ($request->experience) {
                $query->where('jobs.experience', $request->experience);
            }

            if (isset($request->currency)) {
                $minSalary = (int) $request->minsalary; // Convert minsalary to integer
                $query->where(function ($query) use ($minSalary) {
                    $query->where('monthly_fixed_salary_min', '>=', $minSalary);
                });
            }

            if ($request->skill) {
                if (preg_match('~[0-9]+~', $request->skill)) {
                    $skill_arr = explode(',', $request->skill);
                    $query->where(function ($query) use ($skill_arr) {
                        foreach ($skill_arr as $skill_id) {
                            $query->Where('jobs.skills_required', 'LIKE', "%,$skill_id,%");
                        }
                    });
                } else {
                    $skill_data = DB::table('skills')->where('skills', 'like', '%' . $request->skill . '%')->where('status', 'active')->first();
                    $skill_arr = explode(',', $skill_data->id);
                    $query->where(function ($query) use ($skill_arr) {
                        foreach ($skill_arr as $skill_id) {
                            $query->Where('jobs.skills_required', $skill_id);
                        }
                    });
                }
            }

            if ($request->sector) {
                $sector_arr = array_map('intval', explode(',', $request->sector));
                $query->whereIn('jobs.sector_id', $sector_arr);
            }

            if ($request->job_type) {
                $job_type_arr = explode(',', $request->job_type);
                $query->where(function ($query) use ($job_type_arr) {
                    foreach ($job_type_arr as $job_type) {
                        $query->orWhere('jobs.job_type', 'LIKE', "%,$job_type,%")
                            ->orWhere('jobs.job_type', 'LIKE', "$job_type,%")
                            ->orWhere('jobs.job_type', 'LIKE', "%,$job_type")
                            ->orWhere('jobs.job_type', 'LIKE', "%$job_type%");
                    }
                });
            }

            if (isset($request->currency)) {
                $currency = $request->currency; // Get the currency from the request
                $minSalary = (int) $request->salary; // Convert minsalary to integer

                $query->where(function ($query) use ($currency, $minSalary) {
                    $query->where('jobs.monthly_fixed_salary_currency', $currency)
                        ->where('jobs.monthly_fixed_salary_min', '>=', $minSalary);
                });
            }

            $query->where('jobs.job_status', '=', 'active');
            $query->orderByDesc('jobs.id');
            $jobs = $query->paginate();

            foreach ($jobs as $job) {
                $app = Applications::where(['user_id' => $id, 'job_id' => $job->id])->first();
                $job->is_applied = $app ? true : false;
            }

            return JobSearchResource::collection($jobs);
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }



    public function getEmployerActiveJobs(Request $request)
    {
        try {
            if (is_numeric($request->job_id)) {
                $jobs = Job::select('id')->where('id', $request->job_id)->first();
            } else {
                $jobs = Job::select('id')->where('job_slug', $request->job_id)->first();
            }
            //$jobs = Jobs::select('id')->where('job_slug', $request->job_id)->first();

            $user_id = $request->user_id;
            $job_id = $jobs->id;
            $jobs_data = Job::findOrFail($job_id);
            $jobs = Job::select('jobs.*', 'countries.country_name', 'company.company_name', 'company.company_logo', 'saved_jobs.id as saved_id', 'company.company_slug')
                ->leftjoin('countries', 'jobs.job_country', '=', 'countries.id')
                ->leftjoin('company', 'jobs.company_id', '=', 'company.id')
                ->leftjoin('saved_jobs', 'jobs.id', '=', 'saved_jobs.job_id')
                ->where('jobs.company_id', $jobs_data->company_id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('id')
                ->get();

            foreach ($jobs as $job) {
                $app = Applications::where(['user_id' => $user_id, 'job_id' => $job->id])->first();
                if ($app) {
                    $job->is_appied = true;
                }
            }

            return response()->json([
                'status' => true,
                'message' => 'All jobs retrieved successfully',
                'data' => $jobs,
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserSingleJob(Request $request)
    {
        try {
            $query = (new Job)
                ->with('company.logo', 'country', 'city')
                ->where('job_status', 'active');

            if (is_numeric($request->job_id)) {
                $query->where('id', $request->job_id);
            } else {
                $query->where('job_slug', $request->job_id);
            }

            $job = $query->first();

            if ($job) {

                $jobsView = new JobsView();
                $jobsView->job_id = $job->id;

                if (Auth::check()) {
                    $app = Applications::where(['user_id' => Auth::id(), 'job_id' => $job->id])->first();
                    if ($app) {
                        $job->is_appied = true;
                    }

                    $jobsView->user_id = Auth::id();
                }

                $jobsView->save();

                return $this->respondWithSuccess(new JobSearchResource($job));
            } else {
                return $this->respondError('Job not exist');
            }
        } catch (Exception $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    public function getActiveJobs()
    {
        $activejobCount = Job::where('job_status', 'active')->count();

        return response()->json([
            'data' => $activejobCount,
            'message' => 'Employees Fetched',
            'status' => true
        ]);
    }

    public function updateSingleJobBackgroundBannerImage(Request $request)
    {
        try {
            $job = Job::find($request->job_id);

            $validator = Validator::make(
                $request->all(),
                [
                    'fk_banner_file_uuid' => 'required',
                ]
            );

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }


            $job->update(['background_banner_image' => $request->fk_banner_file_uuid]);

            return response()->json(['message' => 'Background banner image updated successfully', 'status' => true], 200);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->errors(), 'status' => false], 422);
        } catch (Exception $e) {
            return response()->json(['message' => 'Failed to update background banner image', 'status' => false], 400);
        }
    }

    public function getAllJobFilter(Request $request, $user_id)
    {
        try {
            $filterJobsData = JobFilter::where('status', 'active')->where('user_id', $user_id)->get();

            if ($filterJobsData->isEmpty()) {
                $newFilterJobData = JobFilter::create([
                    'user_id' => $user_id,
                    'section_name' => 'All Resumes'
                ]);

                $filterJobsData = [$newFilterJobData];
            }

            return response()->json([
                'status' => true,
                'message' => 'All jobs filter retrieved successfully',
                'data' => $filterJobsData,
            ], 200);
        } catch (\PDOException | \Illuminate\Database\QueryException $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }


    public function UpdateSaveJobFilter(Request $request)
    {
        try {

            if ($request->filter_id) {

                $jobsfilter = JobFilter::find($request->filter_id);
                $jobsfilter->section_name = $request->section_name;
                $jobsfilter->user_id = $request->user_id;
                $jobsfilter->job_title = isset($request->keywords) ? $request->keywords : null;
                $jobsfilter->country_id = isset($request->location) ? $request->location : null;
                $jobsfilter->currency = $request->currency;
                $jobsfilter->salary = $request->minsalary;
                $jobsfilter->experience = $request->experience;
                $jobsfilter->skills = $request->skill;
                $jobsfilter->sector = $request->sector;
                $jobsfilter->job_type = $request->job_type;


                $jobsfilter->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Section name updated successfully',
                    'id' => $request->filter_id,
                    'data' => $jobsfilter
                ], 200);
            } else {

                $jobsfilter = new JobFilter();
                $jobsfilter->section_name = $request->section_name;
                $jobsfilter->user_id = $request->user_id;
                $jobsfilter->job_title = $request->keywords;
                $jobsfilter->country_id = $request->location;
                $jobsfilter->currency = $request->currency;
                $jobsfilter->salary = $request->minsalary;
                $jobsfilter->experience = $request->experience;
                $jobsfilter->skills = $request->skill;
                $jobsfilter->sector = $request->sector;
                $jobsfilter->job_type = $request->job_type;

                $jobsfilter->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Section name added successfully',
                    'id' => $jobsfilter->id,
                    'data' => JobFilter::find($jobsfilter->id)

                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function DeleteJobFilter(Request $request)
    {

        $jobsfilter = JobFilter::find($request->id);
        $jobsfilter->status = 'delete';
        $jobsfilter->save();

        return response()->json([
            'status' => true,
            'message' => 'Job Filter Section has been deleted successfully!',
        ], 200);
        try {
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getUserAllJobs(Request $request)
    {
        try {
            $jobs = Job::select('jobs.*', 'countries.country_name as job_country_name', 'company.company_logo', 'company.company_slug')
                ->leftJoin('countries', 'jobs.job_country', '=', 'countries.id')
                ->leftjoin('company', 'jobs.company_id', '=', 'company.id')
                ->where('jobs.user_id', '=', $request->user_id)
                ->where('jobs.job_status', '=', 'active')
                ->orderByDesc('jobs.id')
                ->get();
            if ($jobs) {
                return response()->json([
                    'status' => true,
                    'message' => 'All jobs retrieved successfully',
                    'data' => $jobs,
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'All jobs not retrieved successfully',
                    'data' => $jobs,
                ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function checkJobApplied(Request $request)
    {
        try {
            if ($request->userId && $request->jobId) {
                $appliedJob = Applications::where(['user_id' => $request->userId, 'job_id' => $request->jobId])->count();
                if ($appliedJob > 0) {
                    return response()->json(['status' => true, 'data' => ['isApplied' => true]], 200);
                } else {
                    return response()->json(['status' => true, 'data' => ['isApplied' => false]], 200);
                }
            } else {
                return response()->json(['status' => false, 'data' => [], 'message' => 'User and job both ids are required!'], 401);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function updateUserIdInJobs(Request $request)
    {
        try {
            $jobs = Job::where('user_id', null)->get();
            foreach ($jobs as $job) {
                $user_id = Company::where('id', $job->company_id)->pluck('user_id')->first();
                $job->user_id = $user_id;
                $job->save();
            }
            return response()->json(['status' => true, 'message' => 'Successful'], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function markAsDeleted()
    {
        // Get IDs of all jobs with status 'active'
        $jobIds = Job::where('job_status', 'active')->pluck('id')->toArray();

        // Update all jobs with status 'active' to 'deleted'
        $affectedRows = Job::whereIn('id', $jobIds)->update(['job_status' => 'deleted']);

        // Return a response with the number of affected rows and the IDs
        return response()->json([
            'message' => "{$affectedRows} active jobs have been marked as deleted.",
            'job_ids' => $jobIds
        ]);
    }

    public function exportJobs(Request $request)
    {
        // dd(1);
        $format = $request->query('format', 'xlsx'); // Default to Excel
        $fileName = 'jobs_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

        return Excel::download(new class implements FromCollection, WithHeadings, WithMapping {
            public function collection()
            {
                return Job::with(['company', 'country', 'related_industry', 'city', 'related_sector']) // Load related data
                    ->select('id', 'job_title', 'available_vacancies', 'job_status', 'created_at', 'company_id', 'user_id', 'monthly_fixed_salary_currency', 'monthly_fixed_salary_max')
                    ->get();
            }

            public function headings(): array
            {
                return ['ID', 'Title', 'Company Name', 'Salary', 'Vacancies', 'Created At'];
            }

            public function map($job): array
            {
                return [
                    $job->id,
                    $job->job_title,
                    optional($job->company)->company_name ?? 'N/A',  // Company Name
                    $job->salary,
                    $job->monthly_fixed_salary_currency . ' ' . $job->monthly_fixed_salary_max,
                    $job->created_at->format('Y-m-d H:i:s'),
                ];
            }
        }, $fileName, $format === 'csv' ? \Maatwebsite\Excel\Excel::CSV : \Maatwebsite\Excel\Excel::XLSX);
    }
}
