<?php

namespace App\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Src\AppFramework\ApiController;
use Symfony\Component\HttpKernel\Exception\HttpException;
use App\Models\Blog;
use App\Models\BlogArticle;
use App\Models\BlogCta;
use App\Models\ExpandJobSearch;
use Illuminate\Support\Facades\Validator; 
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogController extends ApiController
{

    public function getAllBlogs(Request $request)
    {
        try {
            $pageSize = $request->get('pageSize');
            $name = $request->get('name');

            $blogs = Blog::where('status', 'active')
                ->with(['author','articles', 'jobSearches','blogCta'])
                // ->with(['author','articles'])
                ->orderBy('created_at', 'desc');

            if ($name) {
                $blogs->when($name, function ($query) use ($name) {
                    $query->where('name', 'LIKE', '%' . $name . '%');
                });
            }

            if ($pageSize) {
                $blogs = $blogs->paginate($pageSize);
            } else {
                $blogs = $blogs->get();
            }

            return response()->json([
                'status' => true,
                'message' => 'Blogs fetched successfully',
                'data' => $blogs
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }



    public function editAndSaveBlogData(Request $request)
    {
            // $validator = Validator::make($request->all(), [
            //     'blog_category_id' => 'required|integer', 
            //     'created_by_id'    => 'required|integer',
            //     'name'             => 'required|string',
            //     'slug'             => 'required|string', 
            //     'author_id'        => 'required|integer', 
            //     'tag'              => 'required|string',
            //     'heading'          => 'required|string',
            //     'description'      => 'required|string',
            //     'meta_tag'         => 'nullable|string|max:255',
            //     'meta_desc'        => 'nullable|string|max:255',
            //     'image'            => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048'
            // ]);
            // if ($validator->fails()) {
            //     return response()->json([
            //         'status' => 'error',
            //         'errors' => $validator->errors(),
            //     ], 422);
            // };
            $blog = Blog::find($request->id);

            if (!$blog) {
                $blog = new Blog();
                $existingBlog = Blog::where('slug', $request->slug)->first();
            } else {
                $existingBlog = Blog::where('slug', $request->slug)
                    ->where('id', '!=', $blog->id)
                    ->first();
            }

            if ($existingBlog) {
                return response()->json([
                    'status' => false,
                    'message' => 'This url is already in the database',
                    'data' => $existingBlog,
                    'error_code' => 'BLOG_SLUG_DUPLICATE'
                ], 422);
            }

            $blog->blog_category_id = $request->blog_category_id;
            $blog->created_by_id = $request->created_by_id;
            $blog->name = $request->name;
            $blog->slug = Str::slug($request->slug, '-');
            $blog->author_id = $request->author_id;
            $blog->tag = $request->tag;
            $blog->heading = $request->heading;
            $blog->description = $request->description;
            $blog->meta_tag = $request->meta_tag;
            $blog->meta_desc = $request->meta_desc;

            if ($request->hasFile('image')) {
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('image');
                $imageName = $randomNumber . $imagePath->getClientOriginalName();
                $path = $imagePath->storeAs('public/images/blogs', $imageName);
                $blog->image = $imageName;
            }
            $blog->save();

            if ($blog->save()) {
                return response()->json(['status' => true, 'message' => 'Blog data has been save successfully']);
            } else {
                return response()->json(['status' => false, 'message' => 'There has been error for saving the blog data']);
            }
    } 

    public function updateBlogData(Request $request, $id)
    {
        try {
            $blog = Blog::find($id);

            if (!$blog) {
                return response()->json([
                    'status' => false,
                    'message' => 'Blog not found',
                    'error_code' => 'BLOG_NOT_FOUND'
                ], 404);
            }

            // Check for slug duplication (excluding current blog)
            if ($request->has('slug')) {
                $existingBlog = Blog::where('slug', Str::slug($request->slug, '-'))
                    ->where('id', '!=', $blog->id)
                    ->first();

                if ($existingBlog) {
                    return response()->json([
                        'status' => false,
                        'message' => 'This URL slug is already in use by another blog',
                        'error_code' => 'BLOG_SLUG_DUPLICATE'
                    ], 422);
                }
            }

            // Update only provided fields
            if ($request->has('blog_category_id')) {
                $blog->blog_category_id = $request->blog_category_id;
            }
            if ($request->has('created_by_id')) {
                $blog->created_by_id = $request->created_by_id;
            }
            if ($request->has('name')) {
                $blog->name = $request->name;
            }
            if ($request->has('slug')) {
                $blog->slug = Str::slug($request->slug, '-');
            }
            if ($request->has('author_id')) {
                $blog->author_id = $request->author_id;
            }
            if ($request->has('tag')) {
                $blog->tag = $request->tag;
            }
            if ($request->has('heading')) {
                $blog->heading = $request->heading;
            }
            if ($request->has('description')) {
                $blog->description = $request->description;
            }
            if ($request->has('meta_tag')) {
                $blog->meta_tag = $request->meta_tag;
            }
            if ($request->has('meta_desc')) {
                $blog->meta_desc = $request->meta_desc;
            }
            if ($request->has('status')) {
                $blog->status = $request->status;
            }

            // Handle image upload
            if ($request->hasFile('image')) {
                $randomNumber = mt_rand(1000000000, 9999999999);
                $imagePath = $request->file('image');
                $imageName = $randomNumber . $imagePath->getClientOriginalName();
                $path = $imagePath->storeAs('public/images/blogs', $imageName);
                $blog->image = $imageName;
            }

            if ($blog->save()) {
                return response()->json([
                    'status' => true,
                    'message' => 'Blog updated successfully',
                    'data' => $blog
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to update blog data'
                ], 500);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getDataByTag($tags)
    {
        $tagsArray = explode(',', $tags);
        $query = Blog::where(function ($query) use ($tagsArray) {
            foreach ($tagsArray as $tag) {
                $query->orWhere('tag', 'LIKE', '%' . $tag . '%');
            }
        })->get();
        return $query;
    }

    public function deleteBlog(Request $request)
    {
        try {

            // $blog = Blog::find($request->id);
            // $blog->status = 'delete';
            // $blog->save();
            // return response()->json([
            //     'status' => true,
            //     'message' => 'Blog has been deleted successfully!',
            // ], 200);
            $blog = Blog::find($request->id);
            if ($blog) {
                $blog->delete();
                return response()->json([
                        'status' => true,
                        'message' => 'Blog has been deleted successfully!',
                    ], 200);
            }
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }

    public function getBlogBySlug($slug): JsonResponse
    {
        try {
            $blog = (new Blog)
                ->where('slug', $slug)
                ->with(['author','articles', 'jobSearches','blogCta'])
                // ->with(['author','articles'])
                ->where('status', 'active')
                ->first();

            $lastEntries = (new Blog)
                ->orderBy('created_at')
                ->with('author')
                ->where('status', 'active')
                ->limit(3)
                ->get();

            if ($blog) {
                return $this->respondWithSuccess([
                    'status'=>true,
                    'entry' => $blog,
                    'last_entries' => $lastEntries
                ]);
            } else {
                return $this->respondWithSuccess([
                    'status'=>false,
                    'message'=>'The blog entry not exists or was disabled'
                ]
                );
            }
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }

    public function getHomeTopBlogs(Request $request)
    {
        try {
            $blogs = Blog::where('status', 'active')
                ->with('author')
                ->orderBy('created_at', 'desc')->take(3)->get();

            return response()->json([
                'status' => true,
                'message' => 'Blogs fetched successfully',
                'data' => $blogs
            ], 200);
        } catch (Exception $e) {
            throw new HttpException(500, $e->getMessage());
        }
    }
}
