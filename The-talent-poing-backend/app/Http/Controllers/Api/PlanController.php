<?php

namespace App\Http\Controllers\Api;

use App\Models\Country;
use App\Models\Plan;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Src\AppFramework\ApiController;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

/**
 * @group Authentication
 */

class PlanController extends ApiController
{
    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $plans = \App\Models\Plan::with('advantages')
            ->where('status', 'active')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $plans
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getAllCurrencies(): JsonResponse
    {
        $currencies = Country::whereNotNull('currency')
            ->where('currency', '!=', '')
            ->pluck('currency')
            ->map(function ($item) {
                return strtoupper($item);
            })
            ->unique()
            ->values()
            ->toArray();

        return response()->json([
            'status' => 'success',
            'data' => $currencies
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function show(Plan $plan): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'data' => $plan->load('advantages')
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $data = $request->validate([
            'plan_title' => 'required|string|max:255',
            'plan_type' => 'required|string|in:Month,Year',
            'plan_points' => 'required|integer|min:1',
            'plan_sub_desc' => 'required|string',
            'plan_currency' => 'required|string|max:10',
            'plan_amount' => 'required|numeric|min:0',
            'available_cv_views' => 'required',
            'cv_views_is_unlimited' => 'nullable|boolean',
            'items' => 'required|array',
            'items.*.text' => 'required|string|max:500',
            'items.*.available' => 'required|boolean',
        ]);

        $plan = Plan::create([
            'plan_title' => $data['plan_title'],
            'plan_type' => $data['plan_type'],
            'plan_points' => $data['plan_points'],
            'plan_sub_desc' => $data['plan_sub_desc'],
            'plan_currency' => $data['plan_currency'],
            'plan_amount' => $data['plan_amount'],
            'available_cv_views' => $data['available_cv_views'],
            'cv_views_is_unlimited' => $data['cv_views_is_unlimited'],
            'status' => 'active',
        ]);

        foreach ($data['items'] as $item) {
            $item['plan_id'] = $plan->id;

            $plan->advantages()->create($item);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Plan created successfully.',
            'data' => $plan->load('advantages'),
        ]);
    }

    public function update(Request $request, Plan $plan): JsonResponse
    {
        $data = $request->validate([
            'plan_title' => 'required|string|max:255',
            'plan_type' => 'required|string|in:Month,Year',
            'plan_points' => 'required|integer|min:1',
            'plan_sub_desc' => 'required|string',
            'plan_currency' => 'required|string|max:10',
            'plan_amount' => 'required|numeric|min:0',
            'available_cv_views' => 'required',
            'cv_views_is_unlimited' => 'nullable|boolean',
            'items' => 'required|array',
            'items.*.text' => 'required|string|max:500',
            'items.*.available' => 'required|boolean',
        ]);

        $plan->update([
            'plan_title' => $data['plan_title'],
            'plan_type' => $data['plan_type'],
            'plan_points' => $data['plan_points'],
            'plan_sub_desc' => $data['plan_sub_desc'],
            'plan_currency' => $data['plan_currency'],
            'plan_amount' => $data['plan_amount'],
            'available_cv_views' => $data['available_cv_views'],
            'cv_views_is_unlimited' => $data['cv_views_is_unlimited'],
        ]);

        $plan->advantages()->delete();

        foreach ($data['items'] as $item) {
            $item['plan_id'] = $plan->id;

            $plan->advantages()->create($item);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Plan updated successfully.',
            'data' => $plan->load('advantages'),
        ]);
    }

    public function destroy(Plan $plan): JsonResponse
    {
        $plan->delete();

        return response()->json([
            'status' => 'success'
        ]);
    }
}
