<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\Collection;
use Src\EmployerManagement\Infrastructure\Resources\EmployeeResource;
use App\Models\User;
use App\Models\CollectionUsers;

class CollectionController extends Controller
{


    public function getCollectionByUserId(Request $request)
    {
        // Validate the input data
        $request->validate([
            'user_id' => 'required|integer', // Ensure user_id is provided and is an integer
        ]);

        // Get the user ID from the request
        $userId = (int)$request->user_id;

        // Find collections created by the given user ID, along with associated user IDs
        $collections = Collection::with('users')->where('created_by', $userId)->get();

        // Check if any collections were found
        if ($collections->isEmpty()) {
            return response()->json(['message' => 'No collections found for this user.'], 404);
        }

        // Prepare the response to include user IDs in each collection
        $response = $collections->map(function ($collection) {
            return [
                'id' => $collection->id,
                'name' => $collection->name,
                'slug' => $collection->slug,
                'created_by' => $collection->created_by,
                'created_at' => $collection->created_at,
                'updated_at' => $collection->updated_at,
                'user_ids' => $collection->users->pluck('id'), // Get user IDs from the pivot table
            ];
        });

        return response()->json(['collections' => $response], 200);
    }


    public function store(Request $request)
    {
        $collectionId = '';
        // Validate the input data
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Store the collection name
        $name = $request->name;


        if ($request->collection_id == '') {
            $user_id = $request->id;

            // Generate a slug from the collection name
            $slug = Str::slug($name . '-' . $user_id, '-');

            // Check if the slug already exists in the collections table
            $existingSlugCount = Collection::where('slug', $slug)
                ->where('created_by', $user_id)
                ->count();

            // If the collection name (slug) already exists, return an error response
            if ($existingSlugCount > 0) {
                return response()->json(['message' => 'You cannot use the same collection name. Please choose a different name.'], 422);
            }

            // Create the collection with the name, slug, and employer's ID
            $collection = Collection::create([
                'name' => $name,
                'slug' => $slug,
                'created_by' => $user_id,  // Store the logged-in employer's ID
            ]);

            $collectionId = $collection->id;
        } else {
            $collectionId = $request->collection_id;
        }

        // Initialize an array to store the user IDs added to the collection
        $addedUserIds = [];



        // Check if user_ids is present and has length greater than 0
        if ($request->has('user_ids') && count($request->user_ids) > 0) {
            // Prepare the data for the pivot table with the created_by field
            foreach ($request->user_ids as $userId) {
                $collection_user = CollectionUsers::create([
                    'collection_id' => $collectionId,
                    'user_id' => $userId
                ]);

                // Add the user ID to the array
                $addedUserIds[] = $userId;
            }
        }
        // return response()->json(['message' => 'Collection created successfully'], 200);
        return response()->json([
            'message' => 'Collection created successfully',
            'collection_id' => $collectionId,
            'user_ids' => $addedUserIds
        ], 200);
    }

    public function usersCollection(Request $request)
    {
        try {
            $employees = (new User)
                ->with('company', 'company.logo', 'country', 'resumes', 'languages', 'work_experience', 'education', 'portfolio')
                ->where('role', 'employee');

            if ($request->current_position) {
                $employees->where('current_position', 'LIKE', '%' . $request->current_position . '%');
            }

            if ($request->job_status) {
                if (is_array($request->job_status)) {
                    $employees->whereIn('job_status', $request->job_status);
                } else {
                    $employees->where('job_status', $request->job_status);
                }
            }

            if ($request->user_ids)
            {
                if (is_array($request->user_ids)) {
                    $employees->whereIn('id', $request->user_ids);
                } else {
                    $employees->where('id', $request->user_ids);
                }
            }

            if (isset($request->available_resume_count))
            {
                $hasResume = filter_var($request->available_resume_count[0], FILTER_VALIDATE_BOOLEAN);

                if ($hasResume) {
                    $employees->whereHas('resumes', function ($q) {
                        $q->whereNotNull('resume_pdf_path')
                            ->where('resume_pdf_path', '!=', '');
                    });
                } else {
                    $employees->whereDoesntHave('resumes');
                }
            }

            if ($request->location) {
                if (is_array($request->location)) {
                    $employees->whereIn('cities', $request->location);
                    $employees->whereIn('countries', $request->location);
                } else {
                    $employees->where('cities', $request->location);
                    $employees->where('countries', $request->location);
                }
            }

            if ($request->country) {
                if (is_array($request->country)) {
                    $employees->whereIn('countries', $request->country);
                } else {
                    $employees->where('countries', $request->country);
                }
            }


            if ($request->experience) {
                $experienceRange = $request->experience;

                if (strpos($experienceRange, '-') !== false) {
                    [$minExperience, $maxExperience] = explode('-', $experienceRange);
                } elseif ($experienceRange == "20") {
                    $minExperience = intval($experienceRange); // 20
                    $maxExperience = null; // There's no upper limit
                } else if ($experienceRange == "fresher") {
                    $employees->where('years_of_experience', "fresher");
                } else {
                    $minExperience = 0;
                    $maxExperience = $experienceRange;
                }

                if ($maxExperience === null) {
                    $employees->where(function ($query) use ($minExperience) {
                        $query->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", -1) AS UNSIGNED) >= ?', [$minExperience])
                            ->orWhere('years_of_experience', 'LIKE', '20+');
                    });
                } else {
                    $employees->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", 1) AS UNSIGNED) <= ?', [$maxExperience])
                        ->whereRaw('CAST(SUBSTRING_INDEX(years_of_experience, "-", -1) AS UNSIGNED) >= ?', [$minExperience]);
                }
            }

            if ($request->salary && $request->currency) {
                $employees->where('currency', 'LIKE', "%($request->currency)%")
                    ->whereBetween(DB::raw('CAST(current_salary AS UNSIGNED)'), [$request->salary[0], $request->salary[1]]);
            }

            if ($request->skills) {
                $skillInput = $request->skills;
                if (is_array($skillInput)) {
                    $employees->where(function ($query) use ($skillInput) {
                        foreach ($skillInput as $skill) {
                            $query->orWhereRaw("FIND_IN_SET(?, skills) > 0", [$skill]);
                        }
                    });
                } else {
                    $employees->whereRaw("FIND_IN_SET(?, skills) > 0", [$skillInput]);
                }
            }

            if ($request->sector) {
                if (is_array($request->sector)) {
                    $employees->whereIn('sector', $request->sector);
                } else {
                    $employees->where('sector', $request->sector);
                }
            }

            if ($request->industry) {
                if (is_array($request->industry)) {
                    $employees->whereIn('industry', $request->industry);
                } else {
                    $employees->where('industry', $request->industry);
                }
            }

            if ($request->activity) {
                $time = (int) $request->activity;
                if ($time == 24) {
                    $employees->where('last_login', '>=', Carbon::now()->subHours($time));
                } else {
                    $employees->where('last_login', '>=', Carbon::now()->subDays($time));
                }
            }

            $users_id = CollectionUsers::where('collection_id', $request->collection_id)->pluck('user_id');

            if ($users_id->isNotEmpty()) {
                $employees->whereIn('id', $users_id);
            } else {
                $collection = Collection::find($request->collection_id);

                if ($collection) {
                    $employees->whereIn('id', $users_id);

                    $employees->where('status', 'active');

                    $employees->orderBy('id', 'desc');

                    return EmployeeResource::collection($employees->paginate(10));
                } else {
                    return response()->json(['message' => 'No data found'], 404);
                }
            }

            $employees->where('status', 'active');

            $employees->orderBy('id', 'desc');

            return EmployeeResource::collection($employees->paginate(10));
        } catch (\Exception $e) {
            // Handle any unexpected errors and return a 500 response
            return response()->json([
                'message' => 'An error occurred while fetching data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function removeUserFromUserCollection(Request $request)
    {
        try {
            $request->validate([
                'user_id_list' => 'required|array',
                'user_id_list.*' => 'required|integer|exists:users,id',
                'collection_id' => 'required|integer|exists:collection_user,collection_id',
            ]);

            $is_removed = false;

            $user_id_list = $request->user_id_list;
            $collection_id = $request->collection_id;

            foreach ($user_id_list as $user_id)
            {
                $collection_user = CollectionUsers::where('collection_id', $collection_id)
                    ->where('user_id', $user_id)
                    ->first();

                if (isset($collection_user))
                {
                    $collection_user->delete();
                    $is_removed = true;
                }
            }

            return response()->json([
                'message' => $is_removed ? 'User or Users successfully removed from the collection' : 'Nothing deleted because the data was not found',
            ]);
        } catch (\Exception $er)
        {
            return response()->json([
                'message' => 'An error occurred while fetching data',
                'error' => $er->getMessage()
            ], 500);
        }
    }
}
