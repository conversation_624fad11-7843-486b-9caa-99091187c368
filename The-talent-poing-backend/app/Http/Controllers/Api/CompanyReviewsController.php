<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ClaimCompany;
use App\Models\Company;
use App\Models\CompanyFollower;
use App\Models\CompanyReview;
use App\Models\CompanyReviewWorkExperience;
use App\Models\DetailReview;
use App\Models\HelpfulReview;
use App\Models\ReportReview;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

class CompanyReviewsController extends Controller
{
    // public function createCompanyReview(Request $request)
    // {
    //     // dd(1);
    //     try {
    //         $company_review = new CompanyReview;
    //         $company_review->user_id = $request->user_id;
    //         $company_review->company_id = $request->company_id;
    //         $company_review->review_title = $request->review_title;
    //         $company_review->review_description = $request->review_description;
    //         // $company_review->status = 'active';
    //         // $company_review->save();

    //         if ($company_review->save()) {
    //             return response()->json(['status' => true, 'message' => 'Review uploaded successfully.']);
    //         } else {
    //             return response()->json(['status' => false, 'message' => 'Error to upload review.']);
    //         }
    //     } catch (\Exception $e) {
    //         throw new HttpException(500, $e->getMessage());
    //     }
    // }

    // public function createCompanyReview(Request $request)
    // {
    //     // Define custom validation messages
    //     $messages = [
    //         'user_id.required' => 'User ID is required.',
    //         'user_id.integer' => 'User ID must be a valid integer.',
    //         'user_id.exists' => 'User ID does not exist.',
    //         'company_id.required' => 'Company ID is required.',
    //         'company_id.integer' => 'Company ID must be a valid integer.',
    //         'company_id.exists' => 'Company ID does not exist.',
    //         'review_title.required' => 'The review title is required.',
    //         'review_title.string' => 'The review title must be a valid string.',
    //         'review_title.max' => 'The review title may not exceed 255 characters.',
    //         'review_description.required' => 'The review description is required.',
    //         'review_description.string' => 'The review description must be a valid text.',
    //         'review_description.max' => 'The review description is too long.',
    //     ];

    //     try {
    //         // Validate incoming request data
    //         $validatedData = $request->validate([
    //             'user_id' => 'required|integer',
    //             'company_id' => 'required|integer',
    //             'rating' => 'required|string|max:255',
    //             'review_title' => 'required|string|max:255',
    //             'review_description' => 'required|string|max:65535', // Adjusted to avoid excessive max length
    //         ], $messages);

    //         // Create and populate the CompanyReview model
    //         $companyReview = new CompanyReview();
    //         $companyReview->user_id = $validatedData['user_id'];
    //         $companyReview->company_id = $validatedData['company_id'];
    //         $companyReview->rating = $validatedData['rating'];
    //         $companyReview->review_title = $validatedData['review_title'];
    //         $companyReview->review_description = $validatedData['review_description'];
    //         // $companyReview->status = 'active'; // Set a default status

    //         // Attempt to save the new review
    //         if ($companyReview->save()) {
    //             return response()->json([
    //                 'status' => true,
    //                 'message' => 'Review uploaded successfully.',
    //                 'data' => $companyReview // Return the saved review data
    //             ], 200);
    //         }

    //         // If save fails, return an error response
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'Failed to upload review. Please try again.'
    //         ], 500);
    //     } catch (\Illuminate\Validation\ValidationException $e) {
    //         // Catch and return validation errors
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'Validation failed.',
    //             'errors' => $e->errors(),
    //         ], 422);
    //     } catch (\Exception $e) {
    //         // Return a structured error response for any other exceptions
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'An error occurred while uploading the review.',
    //             'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.' // Hide error in production
    //         ], 500);
    //     }
    // }

    public function createCompanyReview(Request $request)
    {
        // Define custom validation messages
        $messages = [
            'user_id.required' => 'User ID is required.',
            'user_id.integer' => 'User ID must be a valid integer.',
            'company_id.required' => 'Company ID is required.',
            'company_id.integer' => 'Company ID must be a valid integer.',
            'review_title.required' => 'The review title is required.',
            'review_description.required' => 'The review description is required.',
            'work_environment.required' => 'Work environment rating is required.',
            'work_life_balance.required' => 'Work-life balance rating is required.',
            'career_growth.required' => 'Career growth rating is required.',
            'compensation_benefit.required' => 'Compensation benefit rating is required.',
            'job_security.required' => 'Job security rating is required.',
            'job_title.required' => 'Job title is required.',
        ];

        try {
            // Validate incoming request data
            $validatedData = $request->validate([
                // Company Review fields
                'user_id' => 'required|integer',
                'company_id' => 'required|integer',
                // 'rating' => 'required|string|max:255',
                'review_title' => 'required|string|max:255',
                'review_description' => 'required|string|max:65535',

                // Detail Review fields
                'work_environment' => 'required|string|max:255',
                'work_life_balance' => 'required|string|max:255',
                'career_growth' => 'required|string|max:255',
                'compensation_benefit' => 'required|string|max:255',
                'job_security' => 'required|string|max:255',

                // Work Experience fields
                'job_title' => 'required|string|max:255',
                'country' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date',
            ], $messages);

            // Calculate average rating from DetailReview fields
            $ratings = [
                $validatedData['work_environment'],
                $validatedData['work_life_balance'],
                $validatedData['career_growth'],
                $validatedData['compensation_benefit'],
                $validatedData['job_security']
            ];
            $averageRating = round(array_sum($ratings) / count($ratings), 1); // Calculate and round to 1 decimal place

            // Create and populate the CompanyReview model
            $companyReview = new CompanyReview();
            $companyReview->user_id = $validatedData['user_id'];
            $companyReview->company_id = $validatedData['company_id'];
            $companyReview->rating = $averageRating;
            $companyReview->review_title = $validatedData['review_title'];
            $companyReview->review_description = $validatedData['review_description'];
            $companyReview->is_approve = 0;

            // Attempt to save the new review
            if ($companyReview->save()) {
                // Create a DetailReview associated with this CompanyReview
                $detailReview = new DetailReview();
                $detailReview->review_id = $companyReview->id;
                $detailReview->work_environment = $validatedData['work_environment'];
                $detailReview->work_life_balance = $validatedData['work_life_balance'];
                $detailReview->career_growth = $validatedData['career_growth'];
                $detailReview->compensation_benefit = $validatedData['compensation_benefit'];
                $detailReview->job_security = $validatedData['job_security'];
                $detailReview->save();

                // Create a CompanyReviewWorkExperience associated with this CompanyReview
                $workExperience = new CompanyReviewWorkExperience();
                $workExperience->review_id = $companyReview->id;
                $workExperience->job_title = $validatedData['job_title'];
                $workExperience->country = $validatedData['country'] ?? null;
                $workExperience->city = $validatedData['city'] ?? null;
                $workExperience->start_date = $validatedData['start_date'] ?? null;
                $workExperience->end_date = $validatedData['end_date'] ?? null;
                $workExperience->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Review uploaded successfully.',
                    'data' => [
                        'company_review' => $companyReview,
                        'detail_review' => $detailReview,
                        'work_experience' => $workExperience
                    ]
                ], 200);
            }

            // If save fails, return an error response
            return response()->json([
                'status' => false,
                'message' => 'Failed to upload review. Please try again.'
            ], 500);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Catch and return validation errors
            return response()->json([
                'status' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            // Return a structured error response for any other exceptions
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while uploading the review.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.'
            ], 500);
        }
    }

    public function helpfulYes(Request $request)
    {
        // Define custom validation messages
        $messages = [
            'review_id.required' => 'The review ID is required.',
            'review_id.integer' => 'The review ID must be a valid integer.',
            'user_id.required' => 'The user ID is required.',
            'user_id.integer' => 'The user ID must be a valid integer.',
        ];

        // Validate incoming request data
        $validatedData = $request->validate([
            'review_id' => 'required|integer',
            'user_id' => 'required|integer',
        ], $messages);

        try {
            // Attempt to find or create the review based on review_id and user_id
            $review = HelpfulReview::firstOrCreate(
                [
                    'review_id' => $validatedData['review_id'],
                    'user_id' => $validatedData['user_id']
                ],
                [
                    'is_helpful' => 1 // Set default attributes if the record is created
                ]
            );

            // Check if the review was just created or already existed
            if ($review->wasRecentlyCreated) {
                $message = 'Review created and marked as helpful.';
            } else {
                // Update is_helpful flag if it already existed and mark as helpful
                $review->is_helpful = 1;
                $review->save();
                $message = 'Review found and marked as helpful.';
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => [
                    'review_id' => $review->id,
                    'is_helpful' => $review->is_helpful,
                    'updated_at' => $review->updated_at
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors
            return response()->json([
                'status' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // Handle general errors
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while marking as helpful.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.'
            ], 500);
        }
    }

    public function helpfulNo(Request $request)
    {
        // Define custom validation messages
        $messages = [
            'review_id.required' => 'The review ID is required.',
            'review_id.integer' => 'The review ID must be a valid integer.',
            'user_id.required' => 'The user ID is required.',
            'user_id.integer' => 'The user ID must be a valid integer.',
        ];

        // Validate incoming request data
        $validatedData = $request->validate([
            'review_id' => 'required|integer',
            'user_id' => 'required|integer',
        ], $messages);

        try {
            // Find or create the review record based on review_id and user_id
            $review = HelpfulReview::firstOrCreate(
                [
                    'review_id' => $validatedData['review_id'],
                    'user_id' => $validatedData['user_id']
                ],
                [
                    'is_helpful' => 2 // Set default value to "not helpful" if creating a new record
                ]
            );

            // Determine if the record was just created or found
            if ($review->wasRecentlyCreated) {
                $message = 'Review created and marked as not helpful.';
            } else {
                // Update is_helpful flag to 2 if it already existed, marking as "not helpful"
                $review->is_helpful = 2;
                $review->save();
                $message = 'Review found and marked as not helpful.';
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => [
                    'review_id' => $review->id,
                    'is_helpful' => $review->is_helpful,
                    'updated_at' => $review->updated_at
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors
            return response()->json([
                'status' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // Handle general errors
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while marking as not helpful.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.'
            ], 500);
        }
    }

    public function reportReview(Request $request)
    {
        // Define custom validation messages
        $messages = [
            'review_id.required' => 'The review ID is required.',
            'review_id.integer' => 'The review ID must be a valid integer.',
            'user_id.required' => 'The user ID is required.',
            'user_id.integer' => 'The user ID must be a valid integer.',
            'report_reason.required' => 'A report reason is required.',
            'report_reason.string' => 'The report reason must be valid text.',
            'report_reason.max' => 'The report reason may not exceed 255 characters.',
        ];

        try {
            // Validate incoming request data
            $validatedData = $request->validate([
                'user_id' => 'required|integer',
                'review_id' => 'required|integer',
                'report_reason' => 'required|string|max:255',
            ], $messages);

            // Use firstOrCreate to find or create a new report
            $report = ReportReview::firstOrCreate(
                [
                    'user_id' => $validatedData['user_id'],
                    'review_id' => $validatedData['review_id']
                ],
                [
                    'report_reason' => $validatedData['report_reason']
                ]
            );

            // Update the report reason if it already exists
            if (!$report->wasRecentlyCreated) {
                $report->report_reason = $validatedData['report_reason'];
                $report->save();
                $message = 'Review report updated successfully.';
            } else {
                $message = 'Review reported successfully.';
            }

            $chageStatus = CompanyReview::where('id', $validatedData['review_id'])->first();
            $chageStatus->is_reported = 1;
            $chageStatus->save();

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => [
                    'review_id' => $report->review_id,
                    'user_id' => $report->user_id,
                    'report_reason' => $report->report_reason,
                    'updated_at' => $report->updated_at
                ]
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Handle validation errors
            return response()->json([
                'status' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            // Log error details for debugging
            \Log::error('Report Review Error: ' . $e->getMessage());

            // Handle general errors
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while processing the report.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.'
            ], 500);
        }
    }

    // public function getSingleCompanyReview(Request $request)
    // {
    //     $id = $request->id;

    //     if (!$id) {
    //         return response()->json([
    //             'status' => 'error',
    //             'message' => 'Company ID is required',
    //             'data' => []
    //         ], 400); // Bad Request
    //     }

    //     // Retrieve all reviews for the specified company with related user and detail review data
    //     $reviews = CompanyReview::where('company_id', $id)
    //         ->with(['workExperience', 'detailReview']) // Eager load user and detail review data
    //         ->get();

    //     // Check if there are reviews for the specified company
    //     if ($reviews->isEmpty()) {
    //         return response()->json([
    //             'status' => 'success',
    //             'message' => 'No reviews found for the specified company',
    //             'data' => [
    //                 'total_reviews' => 0,
    //                 'average_rating' => 0,
    //                 'five_star_reviews' => 0,
    //                 'four_star_reviews' => 0,
    //                 'three_star_reviews' => 0,
    //                 'two_star_reviews' => 0,
    //                 'one_star_reviews' => 0,
    //                 'reviews' => []
    //             ]
    //         ], 200); // OK
    //     }

    //     // Calculate total number of reviews and average rating
    //     $totalReviews = $reviews->count();
    //     $averageRating = round($reviews->avg('rating'), 1);

    //     // Count reviews by each star rating
    //     $fiveStarCount = $reviews->where('rating', '5')->count();
    //     $fourStarCount = $reviews->where('rating', '4')->count();
    //     $threeStarCount = $reviews->where('rating', '3')->count();
    //     $twoStarCount = $reviews->where('rating', '2')->count();
    //     $oneStarCount = $reviews->where('rating', '1')->count();

    //     // Structure the response to include review statistics and detailed review list
    //     $response = [
    //         'status' => 'success',
    //         'message' => 'Company reviews retrieved successfully',
    //         'data' => [
    //             'total_reviews' => $totalReviews,
    //             'average_rating' => $averageRating,
    //             'five_star_reviews' => $fiveStarCount,
    //             'four_star_reviews' => $fourStarCount,
    //             'three_star_reviews' => $threeStarCount,
    //             'two_star_reviews' => $twoStarCount,
    //             'one_star_reviews' => $oneStarCount,
    //             'reviews' => $reviews->map(function ($review) {
    //                 return [
    //                     'id' => $review->id,
    //                     'rating' => $review->rating,
    //                     'review_title' => $review->review_title,
    //                     'review_description' => $review->review_description,
    //                     'helpful_yes_count' => $review->helpful_yes_count,
    //                     'helpful_no_count' => $review->helpful_no_count,
    //                     'created_at' => $review->created_at,
    //                     'work_experience' => $review->workExperience ? [
    //                         'job_title' => $review->workExperience->job_title,
    //                         'country' => $review->workExperience->country,
    //                         'city' => $review->workExperience->city,
    //                         'start_date' => $review->workExperience->start_date,
    //                         'end_date' => $review->workExperience->end_date,
    //                     ] : null,
    //                     'detail_review' => $review->detailReview ? [
    //                         'work_environment' => $review->detailReview->work_environment,
    //                         'work_life_balance' => $review->detailReview->work_life_balance,
    //                         'career_growth' => $review->detailReview->career_growth,
    //                         'compensation_benefit' => $review->detailReview->compensation_benefit,
    //                         'job_security' => $review->detailReview->job_security,
    //                     ] : null
    //                 ];
    //             })
    //         ]
    //     ];

    //     // Return the structured response as JSON
    //     return response()->json($response, 200); // OK
    // }

    public function getSingleCompanyReview(Request $request)
    {
        $id = $request->id;

        //new changes ...pass user_id
        $user_id = $request->user_id;

        // Validate input
        if (!$id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Company ID is required',
                'data' => []
            ], 400); // Bad Request
        }

        // Retrieve filters from request
        $jobTitle = $request->get('job_title', ''); // Filter by job title
        $location = $request->get('location', ''); // Filter by location (e.g., country/city)
        $sortBy = $request->get('sort_by', 'most_recent'); // Sorting option (default: most_recent)

        // Fetch reviews for the specified company with filters applied
        $reviews = CompanyReview::where('company_id', $id)
            ->when($jobTitle, function ($query) use ($jobTitle) {
                $query->whereHas('workExperience', function ($subQuery) use ($jobTitle) {
                    $subQuery->where('job_title', 'like', '%' . $jobTitle . '%');
                });
            })
            ->when($location, function ($query) use ($location) {
                $query->whereHas('workExperience', function ($subQuery) use ($location) {
                    $subQuery->where('country', 'like', '%' . $location . '%')
                        ->orWhere('city', 'like', '%' . $location . '%');
                });
            })
            ->when($sortBy === 'most_recent', function ($query) {
                $query->orderBy('created_at', 'desc'); // Sort by most recent
            })
            ->when($sortBy === 'oldest', function ($query) {
                $query->orderBy('created_at', 'asc'); // Sort by oldest
            })
            ->with(['workExperience', 'detailReview']) // Eager load workExperience and detailReview
            ->withCount([
                'helpfulReview as helpful_yes_count' => function ($query) {
                    $query->where('is_helpful', 1);
                },
                'helpfulReview as helpful_no_count' => function ($query) {
                    $query->where('is_helpful', 2);
                }
            ])
            ->get();

        // Check if there are reviews for the specified company
        if ($reviews->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'message' => 'No reviews found for the specified company',
                'data' => [
                    'total_reviews' => 0,
                    'average_rating' => 0,
                    'five_star_reviews' => 0,
                    'four_star_reviews' => 0,
                    'three_star_reviews' => 0,
                    'two_star_reviews' => 0,
                    'one_star_reviews' => 0,
                    'reviews' => []
                ]
            ], 200); // OK
        }

        // Calculate total number of reviews and average rating
        $totalReviews = $reviews->count();
        $averageRating = round($reviews->avg('rating'), 1);

        // Count reviews by each star rating
        $fiveStarCount = $reviews->where('rating', '5')->count();
        $fourStarCount = $reviews->where('rating', '4')->count();
        $threeStarCount = $reviews->where('rating', '3')->count();
        $twoStarCount = $reviews->where('rating', '2')->count();
        $oneStarCount = $reviews->where('rating', '1')->count();

        $userHelpfulYesReviews = HelpfulReview::where('user_id', $user_id)->where('is_helpful', 1)->pluck('review_id')->toArray();
        $userHelpfulNoReviews = HelpfulReview::where('user_id', $user_id)->where('is_helpful', 2)->pluck('review_id')->toArray();
        // dd($userHelpfulReviews);
        $userReportedReviews = ReportReview::where('user_id', $user_id)->pluck('review_id')->toArray();
        $userClaimedReviews = ClaimCompany::where('user_id', $user_id)->pluck('company_id')->toArray();
        $userFollowedReviews = CompanyFollower::where('user_id', $user_id)->pluck('company_id')->toArray();

        // Structure the response to include review statistics and detailed review list
        $response = [
            'status' => 'success',
            'message' => 'Company reviews retrieved successfully',
            'data' => [
                'total_reviews' => $totalReviews,
                'average_rating' => $averageRating,
                'five_star_reviews' => $fiveStarCount,
                'four_star_reviews' => $fourStarCount,
                'three_star_reviews' => $threeStarCount,
                'two_star_reviews' => $twoStarCount,
                'one_star_reviews' => $oneStarCount,

                'claim_flag' => in_array($id, $userClaimedReviews),
                'follow_flag' => in_array($id, $userFollowedReviews),

                'reviews' => $reviews->map(function ($review) use ($userHelpfulYesReviews, $userHelpfulNoReviews, $userReportedReviews, $userClaimedReviews, $userFollowedReviews, $id) {
                    return [
                        'id' => $review->id,
                        'rating' => $review->rating,
                        'review_title' => $review->review_title,
                        'review_description' => $review->review_description,
                        'helpful_yes_count' => $review->helpful_yes_count,
                        'helpful_no_count' => $review->helpful_no_count,
                        'created_at' => $review->created_at,
                        'work_experience' => $review->workExperience ? [
                            'job_title' => $review->workExperience->job_title,
                            'country' => $review->workExperience->country,
                            'city' => $review->workExperience->city,
                            'start_date' => $review->workExperience->start_date,
                            'end_date' => $review->workExperience->end_date,
                        ] : null,
                        'detail_review' => $review->detailReview ? [
                            'work_environment' => $review->detailReview->work_environment,
                            'work_life_balance' => $review->detailReview->work_life_balance,
                            'career_growth' => $review->detailReview->career_growth,
                            'compensation_benefit' => $review->detailReview->compensation_benefit,
                            'job_security' => $review->detailReview->job_security,
                        ] : null,
                        'helpful_yes_flag' => in_array($review->id, $userHelpfulYesReviews),
                        'helpful_no_flag' => in_array($review->id, $userHelpfulNoReviews),
                        'report_flag' => in_array($review->id, $userReportedReviews),
                        // 'claim_flag' => in_array($id, $userClaimedReviews),
                        // 'follow_flag' => in_array($id, $userFollowedReviews),
                    ];
                })
            ]
        ];

        // Return the structured response as JSON
        return response()->json($response, 200); // OK
    }





    // ADMIN PART //
    public function approveOrRemoveReview(Request $request)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'company_id' => 'required|integer',
            'review_id' => 'required|integer',
            'action' => 'required|in:approve,remove', // Ensure the action is either approve or remove
        ]);

        try {
            // Find the review based on company_id and review_id
            $review = CompanyReview::where('id', $validatedData['review_id'])
                ->where('company_id', $validatedData['company_id'])
                ->first();

            if (!$review) {
                return response()->json([
                    'status' => false,
                    'message' => 'Review not found for the specified company.',
                ], 404); // Not Found
            }

            // Update the is_approve column based on the action
            $review->is_approve = $validatedData['action'] === 'approve' ? 1 : 2;
            $review->save();

            return response()->json([
                'status' => true,
                'message' => $validatedData['action'] === 'approve'
                    ? 'Review approved successfully.'
                    : 'Review removed successfully.',
                'data' => $review
            ], 200); // OK
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while processing the request.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500); // Internal Server Error
        }
    }

    public function removeOrRejectReport(Request $request)
    {
        // Validate the request data
        $validatedData = $request->validate([
            'company_id' => 'required|integer',
            'review_id' => 'required|integer',
            'action' => 'required|in:reject,remove', // Ensure the action is either reject or remove
        ]);

        try {
            // Find the review based on company_id and review_id
            $review = CompanyReview::where('id', $validatedData['review_id'])
                ->where('company_id', $validatedData['company_id'])
                ->first();

            if (!$review) {
                return response()->json([
                    'status' => false,
                    'message' => 'Review not found for the specified company.',
                ], 404); // Not Found
            }

            // Handle reject action
            if ($validatedData['action'] === 'reject') {
                $review->is_reported = 0; // Mark as not reported
            }

            // Handle remove action
            if ($validatedData['action'] === 'remove') {
                $review->is_reported = 0; // Mark as not reported
                $review->is_approve = 2; // Mark as removed
            }

            // Save the updated review
            $review->save();

            return response()->json([
                'status' => true,
                'message' => $validatedData['action'] === 'reject'
                    ? 'Review report rejected successfully.'
                    : 'Review removed successfully.',
                'data' => $review
            ], 200); // OK
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while processing the request.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500); // Internal Server Error
        }
    }

    public function deleteReview($review_id)
    {
        // Find the review by its ID
        $review = CompanyReview::find($review_id);

        // Check if the review exists
        if ($review) {
            // Delete the review
            $review->delete();

            // Optionally, you can return a success message or response
            return response()->json([
                'message' => 'Review deleted successfully.'
            ], 200);
        } else {
            // Return an error if the review was not found
            return response()->json([
                'error' => 'Review not found.'
            ], 404);
        }
    }


    // public function getReviewsGroupedByCompany()
    // {
    //     try {
    //         // Fetch reviews grouped by company_id
    //         $reviews = CompanyReview::select('company_id')
    //             ->selectRaw('COUNT(id) as total_reviews')
    //             ->selectRaw('AVG(rating) as average_rating')
    //             ->selectRaw('SUM(helpful_yes_count) as total_helpful_yes')
    //             ->selectRaw('SUM(helpful_no_count) as total_helpful_no')
    //             ->selectRaw('SUM(CASE WHEN is_approve = 1 THEN 1 ELSE 0 END) as approved_reviews')
    //             ->selectRaw('SUM(CASE WHEN is_reported = 1 THEN 1 ELSE 0 END) as reported_reviews')
    //             ->groupBy('company_id')
    //             ->with('company:id,company_name') // Assuming a relationship exists with the Company model
    //             ->get();

    //         // Format response
    //         $response = $reviews->map(function ($review) {
    //             return [
    //                 'company_id' => $review->company_id,
    //                 'company_name' => $review->company->company_name ?? 'Unknown Company',
    //                 'total_reviews' => $review->total_reviews,
    //                 'average_rating' => round($review->average_rating, 1),
    //                 'total_helpful_yes' => $review->total_helpful_yes,
    //                 'total_helpful_no' => $review->total_helpful_no,
    //                 'approved_reviews' => $review->approved_reviews,
    //                 'reported_reviews' => $review->reported_reviews,
    //             ];
    //         });

    //         return response()->json([
    //             'status' => true,
    //             'message' => 'Reviews grouped by company retrieved successfully.',
    //             'data' => $response
    //         ], 200);
    //     } catch (\Exception $e) {
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'An error occurred while fetching the reviews.',
    //             'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
    //         ], 500);
    //     }
    // }

    // public function getReviewsGroupedByCompany(Request $request)
    // {
    //     try {
    //         // Validate the incoming request for sorting
    //         $validatedData = $request->validate([
    //             'sort_by' => 'nullable|string|in:rating,approval,reported,helpful_yes,helpful_no', // Supported sorting fields
    //             'order' => 'nullable|string|in:asc,desc', // Sorting order: ascending or descending
    //         ]);

    //         // Determine sorting column and order
    //         $sortBy = $request->get('sort_by', 'rating'); // Default sort by 'rating'
    //         $order = $request->get('order', 'desc'); // Default order is 'desc'

    //         // Map the sort_by value to the actual column names in the database
    //         $sortableColumns = [
    //             'rating' => 'average_rating',
    //             'approval' => 'approved_reviews',
    //             'reported' => 'reported_reviews',
    //             'helpful_yes' => 'total_helpful_yes',
    //             'helpful_no' => 'total_helpful_no',
    //         ];
    //         $sortColumn = $sortableColumns[$sortBy] ?? 'average_rating';

    //         // Fetch reviews grouped by company_id
    //         $reviews = CompanyReview::select('company_id')
    //             ->selectRaw('COUNT(id) as total_reviews')
    //             ->selectRaw('AVG(rating) as average_rating')
    //             ->selectRaw('SUM(helpful_yes_count) as total_helpful_yes')
    //             ->selectRaw('SUM(helpful_no_count) as total_helpful_no')
    //             ->selectRaw('SUM(CASE WHEN is_approve = 1 THEN 1 ELSE 0 END) as approved_reviews')
    //             ->selectRaw('SUM(CASE WHEN is_reported = 1 THEN 1 ELSE 0 END) as reported_reviews')
    //             ->groupBy('company_id')
    //             ->with('company:id,company_name') // Assuming a relationship exists with the Company model
    //             ->orderBy($sortColumn, $order) // Apply dynamic sorting
    //             ->get();

    //         // Format response
    //         $response = $reviews->map(function ($review) {
    //             return [
    //                 'company_id' => $review->company_id,
    //                 'company_name' => $review->company->company_name ?? 'Unknown Company',
    //                 'total_reviews' => $review->total_reviews,
    //                 'average_rating' => round($review->average_rating, 1),
    //                 'total_helpful_yes' => $review->total_helpful_yes,
    //                 'total_helpful_no' => $review->total_helpful_no,
    //                 'approved_reviews' => $review->approved_reviews,
    //                 'reported_reviews' => $review->reported_reviews,
    //             ];
    //         });

    //         return response()->json([
    //             'status' => true,
    //             'message' => 'Reviews grouped by company retrieved successfully.',
    //             'data' => $response,
    //         ], 200);
    //     } catch (\Exception $e) {
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'An error occurred while fetching the reviews.',
    //             'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
    //         ], 500);
    //     }
    // }

    // public function manageSingleCompanyReview(Request $request)
    // {
    //     // Validate company_id
    //     $validatedData = $request->validate([
    //         'company_id' => 'required|integer',
    //     ]);

    //     // Fetch the company with reviews and related data
    //     $company = Company::with([
    //         'reviews.detailReview',          // Detailed review relationship
    //         'reviews.workExperience',       // Work experience relationship
    //         'reviews.reportReviews',        // Report reviews relationship
    //         'reviews.helpfulReview',        // Helpful review relationship
    //         'reviews.userWorkExperiences',  // User work experience relationship
    //     ])
    //         ->where('id', $validatedData['company_id'])
    //         ->first();

    //     // Check if the company exists
    //     if (!$company) {
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'Company not found'
    //         ], 404);
    //     }

    //     // Extract the company reviews
    //     $companyReviews = $company->reviews;

    //     // Prepare the response
    //     $response = [
    //         'status' => true,
    //         'message' => 'Company reviews retrieved successfully.',
    //         'company_avg_rating' => round($companyReviews->avg('rating'), 1),
    //         'total_reviews' => $companyReviews->count(),
    //         'company_sector' => $company->company_sector,    // Assuming 'company_sector' is a field
    //         'company_location' => $company->company_location, // Assuming 'company_location' is a field
    //         'no_of_employees' => $company->no_of_employees, // Assuming 'no_of_employees' is a field
    //         'data' => $companyReviews->map(function ($review) {
    //             return [
    //                 'review_title' => $review->review_title,
    //                 'review_description' => $review->review_description,
    //                 'detailed_review' => [
    //                     'work_environment' => $review->detailReview->work_environment ?? null,
    //                     'work_life_balance' => $review->detailReview->work_life_balance ?? null,
    //                     'career_growth' => $review->detailReview->career_growth ?? null,
    //                     'compensation_benefit' => $review->detailReview->compensation_benefit ?? null,
    //                     'job_security' => $review->detailReview->job_security ?? null
    //                 ],
    //                 'work_experience' => [
    //                     'job_title' => $review->workExperience->job_title ?? null,
    //                     'country' => $review->workExperience->country ?? null,
    //                     'city' => $review->workExperience->city ?? null,
    //                     'start_date' => $review->workExperience->start_date ?? null,
    //                     'end_date' => $review->workExperience->end_date ?? null
    //                 ],
    //                 'helpful_yes_count' => $review->helpfulReview->where('is_helpful', 1)->count(),
    //                 'helpful_no_count' => $review->helpfulReview->where('is_helpful', 2)->count(),
    //                 'report_reviews' => $review->reportReviews->map(function ($reportReview) {
    //                     return [
    //                         'id' => $reportReview->id,
    //                         'report_reason' => $reportReview->report_reason,
    //                         'user_id' => $reportReview->user_id
    //                     ];
    //                 }),
    //                 'user_work_experiences' => $review->userWorkExperiences->map(function ($workExperience) {
    //                     return [
    //                         'job_title' => $workExperience->job_title ?? null,
    //                         'country' => $workExperience->country ?? null,
    //                         'city' => $workExperience->city ?? null,
    //                         'start_date' => $workExperience->start_date ?? null,
    //                         'end_date' => $workExperience->end_date ?? null
    //                     ];
    //                 })
    //             ];
    //         }),
    //     ];

    //     // Return the response as JSON
    //     return response()->json($response, 200);
    // }


    // public function manageSingleCompanyReview(Request $request)
    // {
    //     // Validate the incoming request
    //     $validatedData = $request->validate([
    //         'company_id' => 'required|integer',
    //         'type' => 'nullable|string|in:all,approved,pending,reported', // Validate the 'type' parameter
    //     ]);

    //     // Default to 'all' if type is not provided
    //     $type = $request->get('type', 'all');

    //     // Fetch the company with reviews and related data
    //     $company = Company::with([
    //         'reviews' => function ($query) use ($type) {
    //             // Filter reviews based on the type
    //             if ($type === 'approved') {
    //                 $query->where('is_approve', 1);
    //             } elseif ($type === 'pending') {
    //                 $query->where('is_approve', 0);
    //             } elseif ($type === 'reported') {
    //                 $query->where('is_reported', 1);
    //             }
    //         },
    //         'reviews.detailReview',          // Detailed review relationship
    //         'reviews.workExperience',       // Work experience relationship
    //         'reviews.reportReviews',        // Report reviews relationship
    //         'reviews.helpfulReview',        // Helpful review relationship
    //         'reviews.userWorkExperiences',  // User work experience relationship
    //     ])
    //         ->where('id', $validatedData['company_id'])
    //         ->first();

    //     // Check if the company exists
    //     if (!$company) {
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'Company not found'
    //         ], 404);
    //     }

    //     // Extract the company reviews
    //     $companyReviews = $company->reviews;

    //     // Prepare the response
    //     $response = [
    //         'status' => true,
    //         'message' => 'Company reviews retrieved successfully.',
    //         'company_avg_rating' => round($companyReviews->avg('rating'), 1),
    //         'total_reviews' => $companyReviews->count(),
    //         'company_sector' => $company->company_sector,    // Assuming 'company_sector' is a field
    //         'company_location' => $company->company_location, // Assuming 'company_location' is a field
    //         'no_of_employees' => $company->no_of_employees, // Assuming 'no_of_employees' is a field
    //         'data' => $companyReviews->map(function ($review) {
    //             return [
    //                 'review_title' => $review->review_title,
    //                 'review_description' => $review->review_description,
    //                 'detailed_review' => [
    //                     'work_environment' => $review->detailReview->work_environment ?? null,
    //                     'work_life_balance' => $review->detailReview->work_life_balance ?? null,
    //                     'career_growth' => $review->detailReview->career_growth ?? null,
    //                     'compensation_benefit' => $review->detailReview->compensation_benefit ?? null,
    //                     'job_security' => $review->detailReview->job_security ?? null
    //                 ],
    //                 'work_experience' => [
    //                     'job_title' => $review->workExperience->job_title ?? null,
    //                     'country' => $review->workExperience->country ?? null,
    //                     'city' => $review->workExperience->city ?? null,
    //                     'start_date' => $review->workExperience->start_date ?? null,
    //                     'end_date' => $review->workExperience->end_date ?? null
    //                 ],
    //                 'helpful_yes_count' => $review->helpfulReview->where('is_helpful', 1)->count(),
    //                 'helpful_no_count' => $review->helpfulReview->where('is_helpful', 2)->count(),
    //                 'report_reviews' => $review->reportReviews->map(function ($reportReview) {
    //                     return [
    //                         'id' => $reportReview->id,
    //                         'report_reason' => $reportReview->report_reason,
    //                         'user_id' => $reportReview->user_id
    //                     ];
    //                 }),
    //                 'user_work_experiences' => $review->userWorkExperiences->map(function ($workExperience) {
    //                     return [
    //                         'job_title' => $workExperience->job_title ?? null,
    //                         'country' => $workExperience->country ?? null,
    //                         'city' => $workExperience->city ?? null,
    //                         'start_date' => $workExperience->start_date ?? null,
    //                         'end_date' => $workExperience->end_date ?? null
    //                     ];
    //                 })
    //             ];
    //         }),
    //     ];

    //     // Return the response as JSON
    //     return response()->json($response, 200);
    // }


    public function getReviewsGroupedByCompany(Request $request)
    {
        try {
            $name = $request->company_name;

            // Validate the incoming request for sorting and filtering
            $validatedData = $request->validate([
                'sort_by' => 'nullable|string|in:rating,approval,reported', // Supported sorting fields
                'order' => 'nullable|string|in:asc,desc', // Sorting order: ascending or descending
                'star' => 'nullable|integer|min:1|max:5' // Filter for "Star Rating & Up"
            ]);

            $pageSize = $request->get('pageSize', 10);

            // Determine sorting column and order
            $sortBy = $request->get('sort_by', 'rating'); // Default sort by 'rating'
            $order = $request->get('order', 'desc'); // Default order is 'desc'

            // Fetch reviews grouped by company_id
            $reviews = CompanyReview::select('company_id')
                ->selectRaw('COUNT(id) as total_reviews')
                ->selectRaw('AVG(rating) as average_rating')
                ->selectRaw('SUM(CASE WHEN is_approve = 1 THEN 1 ELSE 0 END) as approved_reviews')
                ->selectRaw('SUM(CASE WHEN is_reported = 1 THEN 1 ELSE 0 END) as reported_reviews')
                ->selectRaw('MAX(updated_at) as last_updated_on') // Add this line
                ->groupBy('company_id')
                ->when($request->filled('star'), function ($query) use ($request) {
                    $query->havingRaw('AVG(rating) >= ?', [$request->star]); // Filter for "Star Rating & Up"
                })
                ->when($name, function ($query) use ($name) {
                    // Filter reviews based on company name using LIKE
                    $query->whereHas('company', function ($query) use ($name) {
                        $query->where('company_name', 'LIKE', '%' . $name . '%');
                    });
                })
                ->with('company:id,company_name,company_slug') // Assuming a relationship exists with the Company model
                // ->orderBy($sortBy === 'rating' ? 'average_rating' : $sortBy, $order) // Apply dynamic sorting
                ->orderBy(
                    $sortBy === 'rating' ? 'average_rating' : ($sortBy === 'approval' ? 'approved_reviews' : ($sortBy === 'reported' ? 'reported_reviews' : 'average_rating')), // Default to 'average_rating' if none match
                    $order
                ) // Apply dynamic sorting
                ->paginate($pageSize);

            // Format response
            $response = $reviews->map(function ($review) {

                // Initialize claim status and company name
                $claimStatus = 'unclaimed'; // Default to 'unclaimed'
                $membership = 1; // Default to 'unclaimed'
                $expire = null; // Default to 'unclaimed'
                $companyName = 'Unknown Company'; // Default company name

                // Check if the company exists
                if ($review->company) {
                    $companyName = $review->company->company_name ?? 'Unknown Company';

                    // Check if the company has a claim
                    if ($review->company->claim) {
                        $claimStatus = $review->company->claim->status; // Get the claim status
                    }
                    if ($review->company->membership) {
                        $membership = $review->company->membership->plan_id; // Get the claim status
                        $expire = $review->company->membership->expire_at; // Get the claim status
                    }
                }

                return [
                    'company_id' => $review->company_id,
                    'company_slug' => $review->company->company_slug ?? null,
                    'company_name' => $review->company->company_name ?? 'Unknown Company',
                    'total_reviews' => $review->total_reviews,
                    'average_rating' => round($review->average_rating, 1),
                    'approved_reviews' => $review->approved_reviews,
                    'reported_reviews' => $review->reported_reviews,
                    'last_updated_on' => $review->last_updated_on,
                    'claim_status' => $claimStatus,
                    'membership' => $membership,
                    'expire' => $expire,
                ];
            });

            $meta = [
                'total' => $reviews->total(),
                'per_page' => $reviews->perPage(),
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'from' => $reviews->firstItem(),
                'to' => $reviews->lastItem(),
            ];

            return response()->json([
                'status' => true,
                'message' => 'Reviews grouped by company retrieved successfully.',
                'data' => $response,
                'meta' => $meta,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while fetching the reviews.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500);
        }
    }

    public function manageSingleCompanyReview(Request $request)
    {
        try {
            // Validate the incoming request
            $validatedData = $request->validate([
                'company_id' => 'required|integer',
                'type' => 'nullable|string|in:all,approved,pending,reported', // Filter type
                'sort_by' => 'nullable|string|in:highest_rated,lowest_rated,most_recent,oldest', // Sorting
                'date_range' => 'nullable|array', // Date range filter
                'date_range.start' => 'nullable|date', // Start date
                'date_range.end' => 'nullable|date',   // End date
                'rating' => 'nullable|string|in:high,moderate,low', // Rating filter
                'location' => 'nullable|array', // Location filter
                'job_title' => 'nullable|array', // Job title filter
            ]);

            // Default values for filters
            $type = $request->get('type', 'all');

            // Fetch the company and its reviews with filters
            $company = Company::with([
                'reviews' => function ($query) use ($type, $validatedData) {
                    // Filter by approval status
                    if ($type === 'approved') {
                        $query->where('is_approve', 1);
                    } elseif ($type === 'pending') {
                        $query->where('is_approve', 0);
                    } elseif ($type === 'reported') {
                        $query->where('is_reported', 1);
                    }

                    // Apply date range filter
                    if (isset($validatedData['date_range']['start']) && isset($validatedData['date_range']['end'])) {
                        $query->whereBetween('created_at', [
                            $validatedData['date_range']['start'],
                            $validatedData['date_range']['end']
                        ]);
                    }

                    // Apply rating filter
                    if (isset($validatedData['rating'])) {
                        if ($validatedData['rating'] === 'high') {
                            $query->whereBetween('rating', [4.0, 5.0]);
                        } elseif ($validatedData['rating'] === 'moderate') {
                            $query->whereBetween('rating', [2.5, 3.9]);
                        } elseif ($validatedData['rating'] === 'low') {
                            $query->whereBetween('rating', [1.0, 2.4]);
                        }
                    }

                    // Apply location filter
                    if (!empty($validatedData['location'])) {
                        $query->whereHas('workExperience', function ($q) use ($validatedData) {
                            $q->whereIn('country', $validatedData['location']);
                        });
                    }

                    // Apply job title filter
                    if (!empty($validatedData['job_title'])) {
                        $query->whereHas('workExperience', function ($q) use ($validatedData) {
                            $q->whereIn('job_title', $validatedData['job_title']);
                        });
                    }

                    // Apply sorting
                    $sortBy = $validatedData['sort_by'] ?? null;
                    if ($sortBy === 'highest_rated') {
                        $query->orderBy('rating', 'desc');
                    } elseif ($sortBy === 'lowest_rated') {
                        $query->orderBy('rating', 'asc');
                    } elseif ($sortBy === 'most_recent') {
                        $query->orderBy('created_at', 'desc');
                    } elseif ($sortBy === 'oldest') {
                        $query->orderBy('created_at', 'asc');
                    }
                },
                'reviews.detailReview',          // Detailed review relationship
                'reviews.workExperience',       // Work experience relationship
                'reviews.reportReviews',        // Report reviews relationship
                'reviews.helpfulReview',        // Helpful review relationship
                'reviews.userWorkExperiences',  // User work experience relationship
                'reviews.user',  // User work experience relationship
            ])->where('id', $validatedData['company_id'])->first();

            // Check if the company exists
            if (!$company) {
                return response()->json([
                    'status' => false,
                    'message' => 'Company not found',
                    'data' => [],
                ], 404);
            }

            // Extract the company reviews
            $companyReviews = $company->reviews;

            // Prepare the response
            $response = [
                'status' => true,
                'message' => 'Company reviews retrieved successfully.',
                'data' => [
                    'company_avg_rating' => round($companyReviews->avg('rating'), 1),
                    'total_reviews' => $companyReviews->count(),
                    'company_sector' => $company->company_sector ?? null,
                    'company_id' => $company->id ?? null,
                    'company_location' => $company->company_location ?? null,
                    'no_of_employees' => $company->no_of_employees ?? null,
                    'reviews' => $companyReviews->map(function ($review) {
                        return [
                            'review_title' => $review->review_title,
                            'review_description' => $review->review_description,
                            /// new add
                            'review_id' => $review->id,
                            'review_rating' => $review->rating,
                            'is_approve' => $review->is_approve,
                            'is_reported' => $review->is_reported,
                            'user_id' => $review->user_id,
                            'user_name' => $review->user->name,
                            ////
                            'detailed_review' => [
                                'work_environment' => $review->detailReview->work_environment ?? null,
                                'work_life_balance' => $review->detailReview->work_life_balance ?? null,
                                'career_growth' => $review->detailReview->career_growth ?? null,
                                'compensation_benefit' => $review->detailReview->compensation_benefit ?? null,
                                'job_security' => $review->detailReview->job_security ?? null,
                            ],
                            'work_experience' => [

                                'job_title' => $review->workExperience->job_title ?? null,
                                'country' => $review->workExperience->country ?? null,
                                'city' => $review->workExperience->city ?? null,
                                'start_date' => $review->workExperience->start_date ?? null,
                                'end_date' => $review->workExperience->end_date ?? null,
                            ],
                            'helpful_yes_count' => $review->helpfulReview->where('is_helpful', 1)->count(),
                            'helpful_no_count' => $review->helpfulReview->where('is_helpful', 2)->count(),
                            'report_reviews' => $review->reportReviews->map(function ($reportReview) {
                                return [
                                    'id' => $reportReview->id,
                                    'report_reason' => $reportReview->report_reason,
                                    'user_id' => $reportReview->user_id,
                                ];
                            }),
                            'user_work_experiences' => $review->userWorkExperiences->map(function ($workExperience) {
                                return [
                                    'company_name' => $workExperience->company ?? null,
                                    'job_title' => $workExperience->job_title ?? null,
                                    'country' => $workExperience->country ?? null,
                                    'city' => $workExperience->city ?? null,
                                    'start_date' => $workExperience->start_date ?? null,
                                    'end_date' => $workExperience->end_date ?? null,
                                ];
                            }),
                        ];
                    }),
                ],
            ];

            // Return the response as JSON
            return response()->json($response, 200);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error fetching company reviews: ', ['error' => $e->getMessage()]);

            // Return error response
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while fetching company reviews.',
                'error' => env('APP_DEBUG') ? $e->getMessage() : 'Please contact support.',
            ], 500);
        }
    }
}
