<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\LanguageName;
use Illuminate\Http\Request;

class LanguageManagementController extends Controller
{
    public function index()
    {
        return LanguageName::query()
            ->orderBy('name')
            ->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:11|unique:language_names,code',
        ]);

        $language = LanguageName::create($validated);

        return response()->json([
            'status' => true,
            'message' => 'Language created successfully',
            'data' => $language
        ], 201);
    }

    public function show($id)
    {
        $language = LanguageName::findOrFail($id);
        return response()->json($language);
    }

    public function update(Request $request, $id)
    {
        $language = LanguageName::findOrFail($id);

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'code' => 'sometimes|required|string|max:11|unique:language_names,code,' . $id,
        ]);

        $language->update($validated);

        return response()->json([
            'status' => true,
            'message' => 'Language updated successfully',
            'data' => $language
        ]);
    }

    public function destroy($id)
    {
        $language = LanguageName::findOrFail($id);
        $language->delete();

        return response()->json([
            'status' => true,
            'message' => 'Deleted successfully.'
        ]);
    }
}
