<?php

namespace Src\EmployerManagement\Infrastructure\Controllers;

use App\Repositories\EmployersRepository;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;
use Src\AppFramework\ApiController;
use Src\EmployerManagement\Application\RegisterEmployerService;
use Src\EmployerManagement\Application\UpdateEmployerService;
use Src\EmployerManagement\Infrastructure\Resources\EmployerResource;
use Throwable;

class EmployerController extends ApiController
{
    public function showEmployer(int $id, EmployersRepository $employersRepository): AnonymousResourceCollection
    {
        $employer = $employersRepository->getEmployersById($id);

        return EmployerResource::collection($employer);
    }

    public function updateEmployer(Request $request, int $id, UpdateEmployerService $updateEmployerService): JsonResponse
    {
        try {
            $updateEmployerService->execute($id, $request->all());
            return $this->respondWithSuccess($request->all());
        } catch (Throwable $e) {
            Log::error($e->getMessage(), $e->getTrace());
            return $this->respondError($e->getMessage());
        }
    }

    public function createEmployer(Request $request): JsonResponse
    {
        try {
            (new RegisterEmployerService)($request->all());
            return $this->respondWithSuccess($request->all());
        } catch (Exception $e) {
            return $this->respondError($e->getMessage());
        }
    }
}
