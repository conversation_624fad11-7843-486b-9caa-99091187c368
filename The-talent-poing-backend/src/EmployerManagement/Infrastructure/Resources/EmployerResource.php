<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\Authentication\Domain\User;
use Src\EmployerManagement\Domain\Enums\AccountTypeEnum;
use Src\EmployerManagement\Domain\Enums\ClaimCompaniesEnum;

/**
 * @mixin User
 */
class EmployerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $available_resume_count = $this->company?->available_resume_count ?? 0;

        if (((int) $available_resume_count) === 100000)
        {
            $available_resume_count = 'Unlimited';
        }

        return [
            'available_resume_count' => $available_resume_count ?? 0,
            'bio' => $this->bio,
            'card_cvv' => $this->card_cvv,
            'card_exp_month' => $this->card_exp_month,
            'card_exp_year' => $this->card_exp_year,
            'card_number' => $this->card_number,
            'card_type' => $this->card_type,
            'company' => new CompanyResource($this->company),
            'created_by_id' => $this->created_by_id,
            'currency' => $this->currency,
            'designation' => $this->current_position,
            'current_salary' => $this->current_salary,
            'date_of_birth' => $this->date_of_birth,
            'desired_salary' => $this->desired_salary,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'facebook_link' => $this->facebook_link,
            'first_login' => $this->first_login,
            'gender' => $this->gender,
            'google_id' => $this->google_id,
            'id' => $this->id,
            'industry' => $this->industry,
            'instagram_link' => $this->instagram_link,
            'is2FA' => $this->is2FA,
            'isShowEmail' => $this->isShowEmail,
            'is_approved' => $this->is_approved,
            'job_status' => $this->job_status,
            'job_type' => $this->job_type,
            'linked_id' => $this->linked_id,
            'linkedin_link' => $this->linkedin_link,
            'login_count' => $this->login_count,
            'name' => $this->name,
            'nationality' => $this->nationality,
            'otp' => $this->otp,
            'profile_complete_percentage' => $this->profile_complete_percentage,
            'profile_image' => $this->profile_image,
            'role' => $this->role,
            'sector' => $this->sector,
            'showcontact_no' => $this->showcontact_no,
            'skills' => $this->skills,
            'slug' => $this->slug,
            'status' => $this->status,
            'twitter_link' => $this->twitter_link,
            'unlock_instant_apply' => $this->unlock_instant_apply,
            'updated_at' => $this->updated_at,
            'website_url' => $this->website_url,
            'where_currently_based' => $this->where_currently_based,
            'where_job_search' => $this->where_job_search,
            'years_of_experience' => $this->years_of_experience,
            'average_rating' => $this->avg_rating ? round($this->avg_rating, 1) : 0,
            'total_reviews' => $this->total_reviews_count ?? 0,
            'city' => $this->city,
            'account_type' => $this->getAccountType(),
            'team_members' => $this->whenLoaded('activeTeamMembers', function () {
                return $this->activeTeamMembers->map(function ($teamMember) {
                    return [
                        'id' => $teamMember->id,
                        'name' => $teamMember->name,
                        'email' => $teamMember->email,
                        'role' => $teamMember->role,
                        'designation' => $teamMember->current_position,
                        'available_resume_count' => $teamMember->available_resume_count,
                        'status' => $teamMember->status,
                    ];
                });
            }),
            'membership' => $this->when($this->membership_id, [
                'id' => $this->membership_id,
                'plan_id' => $this->membership_plan_id,
                'expire_at' => $this->membership_expire_at,
                'purchase_at' => $this->membership_purchase_at,
                'status' => $this->membership_status,
                'invoice_number' => null,
                'information' => null,
                'plan' => $this->when($this->plan_id, [
                    'id' => $this->plan_id,
                    'title' => $this->plan_title,
                    'sub_desc' => $this->plan_sub_desc,
                    'currency' => $this->plan_currency,
                    'amount' => $this->plan_amount,
                    'type' => $this->plan_type,
                    'points' => $this->plan_points,
                    'status' => $this->plan_status,
                ]),
            ]),
            'active_job_count' => $this->jobs_count, // Count of active jobs
            'claim_status' => $this->whenLoaded('company', function () {
                return [
                    'id' => $this->claim_companies_id,
                    'status' => $this->claim_status,
                    'message' => $this->claim_companies_message,
                    'claim_date' => $this->claim_companies_created_at,
                    'user' => [
                        'id' => $this->claim_user_id,
                        'name' => $this->claim_user_name,
                        'email' => $this->claim_user_email,
                        'phone' => $this->claim_user_phone,
                        'role' => $this->claim_user_role,
                        'designation' => $this->claim_user_designation,
                    ]
                ];
            }),
        ];
    }

    private function getAccountType(): string
    {
        // if company does not have write in claim companies or claim status = rejected, than Unclaimed
        if (!$this->claim_companies_id || $this->claim_status === ClaimCompaniesEnum::REJECTED->value) {
            return AccountTypeEnum::UNCLAIMED->value;
        }
        
        if ($this->claim_status === AccountTypeEnum::REQUESTED->value) {
            return AccountTypeEnum::REQUESTED->value;
        }
        
        if (!$this->membership_plan_id) {
            return AccountTypeEnum::FREE->value;
        }

        if ($this->claim_status === ClaimCompaniesEnum::APPROVED->value) {
            return match($this->membership_plan_id) {
                1 => AccountTypeEnum::FREE->value,
                2 => AccountTypeEnum::PRO->value,
                default => AccountTypeEnum::FREE->value
            };
        }

        return AccountTypeEnum::UNCLAIMED->value;
    }
}
