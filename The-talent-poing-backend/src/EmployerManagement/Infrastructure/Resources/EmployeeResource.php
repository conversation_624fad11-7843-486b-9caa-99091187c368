<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use App\Models\Cities;
use App\Models\Country;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\Authentication\Domain\User;
use Src\FileManagement\Infrastructure\Resources\FileResource;

/**
 * @mixin User
 */
class EmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $country_name       = null;
        $city_name          = null;
        $nationality_name   = null;

        if ($this->countries)
        {
            $country_obj = Country::find($this->countries);

            if ($country_obj)
            {
                $country_name = $country_obj->country_name;
            }
        }

        if ($this->cities)
        {
            $city_obj = Cities::find($this->cities);

            if ($city_obj)
            {
                $city_name = $city_obj->city_name;
            }
        }

        if ($this->nationality)
        {
            $country_obj = Country::find($this->nationality);

            if ($country_obj)
            {
                $nationality_name = $country_obj->country_name;
            }
        }

        $age = null;

        if ($this->date_of_birth)
        {
            $carbon_parse_age = Carbon::parse($this->date_of_birth);

            if ($carbon_parse_age)
            {
                $age = $carbon_parse_age->age;
            }
        }

        return [
            'id' => $this->id,
            'available_resume_count' => $this->available_resume_count,
            'bio' => $this->bio,
            'created_by_id' => $this->created_by_id,
            'currency' => $this->currency,
            'current_position' => $this->current_position,
            'current_salary' => $this->current_salary,
            'company' => new CompanyResource($this->whenLoaded('company')),
            'country' => $this->whenLoaded('country'),
            'resumes' => $this->whenLoaded('resumes'),
            'languages' => $this->whenLoaded('languages'),
            'work_experience'=> $this->whenLoaded('work_experience'),
            'education' => $this->whenLoaded('education'),
            'portfolio' => $this->whenLoaded('portfolio'),
            'date_of_birth' => $this->date_of_birth,
            'desired_salary' => $this->desired_salary,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'facebook_link' => $this->facebook_link,
            'first_login' => $this->first_login,
            'gender' => $this->gender,
            'google_id' => $this->google_id,
            'industry' => $this->industry,
            'instagram_link' => $this->instagram_link,
            'is2FA' => $this->is2FA,
            'isShowEmail' => $this->isShowEmail,
            'is_approved' => $this->is_approved,
            'job_status' => $this->job_status,
            'job_type' => $this->job_type,
            'linked_id' => $this->linked_id,
            'linkedin_link' => $this->linkedin_link,
            'name' => $this->name,
            'nationality' => $this->nationality,
            'profile_complete_percentage' => $this->profile_complete_percentage,
            // 'profile_image' => new FileResource($this->whenLoaded('profile')),
            'profile_image' => $this->profile ? new FileResource($this->profile) : null,
            'role' => $this->role,
            'sector' => $this->sector,
            'showcontact_no' => $this->showcontact_no,
            'contact_no'  => $this->contact_no,
            'skills' => $this->skills,
            'slug' => $this->slug,
            'status' => $this->status,
            'twitter_link' => $this->twitter_link,
            'unlock_instant_apply' => $this->unlock_instant_apply,
            'updated_at' => $this->updated_at,
            'website_url' => $this->website_url,
            'where_currently_based' => $this->where_currently_based,
            'cities'=>$this->cities,
            'countries'=>$this->countries,
            'where_job_search' => $this->where_job_search,
            'years_of_experience' => $this->years_of_experience,
            'countries_value' => $country_name,
            'cities_value' => $city_name,
            'nationality_value' => $nationality_name,

            'country_name' => $country_name,
            'city_name' => $city_name,
            'nationality_name' => $nationality_name,
            'age' => $age,
            'application' => $this->applicationForJob,
            'resume_viewed' => $this->applicationForJob?->resume_viewed,
        ];
    }
}
