<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\FileManagement\Infrastructure\Resources\FileResource;
use Src\EmployerManagement\Infrastructure\Resources\JobCollectionResource;
use Src\JobsManagement\Infrastructure\Resources\JobSearchResource;

/**
 * @mixin Company
 */
class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $company_logo_url = '';
        if ($this->whenLoaded('logo') && $this->logo)
        {
            $fileResource = new FileResource($this->logo);
            $logoData = $fileResource->toArray($request);
            $company_logo_url = $logoData['source'] ?? '';
        }

        $member_count = User::where('status', 'active')
            ->where('created_by_id', $this->user_id)
            ->where('role', 'staff')
            ->count();

        return [
            'available_resume_count' => $this->available_resume_count,
            'background_banner_image' => $this->background_banner_image,
            'company_contact_no' => $this->company_contact_no,
            'company_description' => $this->company_description,
            'company_email' => $this->company_email,
            'total_staff' => $member_count,
            // 'company_location' => $this->company_location,
            'company_location' => $this->whenLoaded('location', function () {
                return $this->location->country_name; // Assuming 'name' is the field for sector name
            }),
//            'company_logo' => $this->company_logo,
            'company_logo' => $company_logo_url,
            'company_name' => $this->company_name,
            // 'company_sector' => $this->company_sector,
            'company_sector' => $this->whenLoaded('sector', function () {
                return $this->sector->sector_name; // Assuming 'name' is the field for sector name
            }),
            'company_slug' => $this->company_slug,
            'company_website' => $this->company_website,
            'created_at' => $this->created_at,
            'designation' => $this->designation,
            'facebook_link' => $this->facebook_link,
            'id' => $this->id,
            'instagram_link' => $this->instagram_link,
            'linkedin_link' => $this->linkedin_link,
            'logo' => new FileResource($this->whenLoaded('logo')),
            'sector' => $this->whenLoaded('sector'),
            'country' => $this->whenLoaded('location'),
            'jobs' => new JobCollectionResource($this->whenLoaded('jobs')),
            'meta_tag' => $this->meta_tag,
            'meta_desc' => $this->meta_desc,
            'no_of_employees' => $this->no_of_employees,
            'status' => $this->status,
            'twitter_link' => $this->twitter_link,
            'updated_at' => $this->updated_at,
            'user_id' => $this->user_id,
            'founded_date' => $this->founded_date,
        ];
    }
}
