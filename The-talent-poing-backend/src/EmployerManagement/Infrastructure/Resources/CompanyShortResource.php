<?php

namespace Src\EmployerManagement\Infrastructure\Resources;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Src\FileManagement\Infrastructure\Resources\FileResource;

/**
 * @mixin Company
 */
class CompanyShortResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'logo' => new FileResource($this->whenLoaded('logo')),
            'background_banner_image' => $this->background_banner_image,
            'company_name' => $this->company_name,
            'company_slug' => $this->company_slug,
            'company_website' => $this->company_website,
            'sector' => $this->whenLoaded('sector'),
            'country' => $this->whenLoaded('location'),
        ];
    }
}
