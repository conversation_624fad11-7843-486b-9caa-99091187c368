<?php

namespace Src\EmployerManagement\Application;

use App\Models\Company;
use App\Models\User;
use App\Models\Location;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class UpdateEmployerService
{
    public function execute(int $employerId, array $data): Company
    {
        return DB::transaction(function () use ($employerId, $data) {
            $user = $this->findUser($employerId);
            $company = $this->getCompany($user);
            
            $this->validateCompanySlug($company, $data);
            $location = $this->getLocation($data);
            
            $this->updateUser($user, $data);
            $this->updateCompany($company, $data, $location);
            
            return $company->fresh();
        });
    }

    private function findUser(int $employerId): User
    {
        $user = User::find($employerId);
        
        if (!$user) {
            throw new ModelNotFoundException('User not found');
        }
        
        return $user;
    }

    private function getCompany(User $user): Company
    {
        if (!$user->company) {
            throw new ModelNotFoundException('Company not found for this user');
        }
        
        return $user->company;
    }

    private function validateCompanySlug(Company $company, array $data): void
    {
        if (!isset($data['company_name'])) {
            return;
        }

        $newSlug = Str::slug($data['company_name'], '-');
        
        if ($company->company_slug === $newSlug) {
            return;
        }

        $existingCompany = Company::where('company_slug', $newSlug)
            ->where('id', '!=', $company->id)
            ->first();

        if ($existingCompany) {
            throw new InvalidArgumentException('Company slug already exists');
        }
    }

    private function getLocation(array $data): ?Location
    {
        $city = $data['city'] ?? null;
        if (empty($city)) {
            return null;
        }

        return Location::query()->firstOrCreate(
            ['location_name' => $city],
            [
                'location_slug' => str_replace(' ', '-', strtolower($city)),
                'status' => 'active'
            ]
        );
    }

    private function updateUser(User $user, array $data): void
    {
        if (isset($data['user_email'])) {
            $this->validateEmail($user, $data['user_email']);
            $user->email = $data['user_email'];
        }

        if (isset($data['user_name'])) {
            $user->name = $data['user_name'];
        }

        if (isset($data['user_position'])) {
            $user->current_position = $data['user_position'];
        }

        if (isset($data['password']) && !empty($data['password'])) {
            $user->password = Hash::make($data['password']);
        }

        $user->save();
    }

    private function validateEmail(User $user, string $email): void
    {
        if ($user->email === $email) {
            return;
        }

        $existingUser = User::where('email', $email)
            ->where('id', '!=', $user->id)
            ->first();

        if ($existingUser) {
            throw new InvalidArgumentException('Email already registered');
        }
    }

    private function updateCompany(Company $company, array $data, ?Location $location): void
    {
        $this->updateCompanyBasicInfo($company, $data, $location);
        $this->updateCompanyContacts($company, $data);
        $this->updateCompanySocialMedia($company, $data);
        $this->updateCompanyMetadata($company, $data, $location);

        $company->save();
    }

    private function updateCompanyBasicInfo(Company $company, array $data, ?Location $location): void
    {
        $basicFields = [
            'company_name' => 'company_name',
            'description' => 'company_description',
            'company_email' => 'company_email',
            'website' => 'company_website',
            'designation' => 'designation',
            'no_of_employees' => 'no_of_employees',
            'fk_logo_file_uuid' => 'fk_logo_file_uuid',
            'sector_id' => 'company_sector'
        ];

        foreach ($basicFields as $dataKey => $companyField) {
            if (isset($data[$dataKey])) {
                $company->{$companyField} = $data[$dataKey];
            }
        }

        if (!empty($location)) {
            $company->company_location = $location->id;
        }

        if (isset($data['company_name'])) {
            $company->company_slug = Str::slug($data['company_name'], '-');
        }
    }

    private function updateCompanyContacts(Company $company, array $data): void
    {
        $contactFields = [
            'company_email' => 'company_email',
            'website' => 'company_website',
            'phone' => 'company_contact_no'
        ];

        foreach ($contactFields as $dataKey => $companyField) {
            if (isset($data[$dataKey])) {
                $company->{$companyField} = $data[$dataKey];
            }
        }
    }

    private function updateCompanySocialMedia(Company $company, array $data): void
    {
        $socialFields = [
            'linkedin_link' => 'linkedin_link',
            'twitter_link' => 'twitter_link',
            'instagram_link' => 'instagram_link',
            'facebook_link' => 'facebook_link'
        ];

        foreach ($socialFields as $dataKey => $companyField) {
            if (isset($data[$dataKey])) {
                $company->{$companyField} = $data[$dataKey];
            }
        }
    }

    private function updateCompanyMetadata(Company $company, array $data, ?Location $location): void
    {
        if (isset($data['company_name']) && $location) {
            $companyName = $data['company_name'];
            
            $company->meta_tag = "{$companyName} Careers - The Talent Point";
            $company->meta_desc = "Apply for jobs by {$companyName}. Register for Free & Search job openings at {$companyName}. Register for Free and Explore Careers in United Arab Emirates.";
        }
    }
}