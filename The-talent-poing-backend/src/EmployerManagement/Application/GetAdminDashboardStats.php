<?php

namespace Src\EmployerManagement\Application;

use App\Models\Job;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class GetAdminDashboardStats
{
    /**
     * @param int $time
     * @param string $periodInterval day|month
     * @return array
     */
    public function __invoke(int $time, string $periodInterval): array
    {
        $dateTo = Carbon::now();
        $dateFrom = $dateTo->copy()->sub($time . ' ' . $periodInterval);

        $result = [];

        while ($dateFrom->lessThanOrEqualTo($dateTo)) {

            $employees = (new User())
                ->where('status', 'active');
            $employers = (new User())
                ->where('status', 'active');

            $jobs = (new Job)
                ->where('job_status', 'active');

            if (str_contains($periodInterval, 'month')) {
                $employees->whereYear('created_at', $dateFrom->year)
                    ->whereMonth('created_at', $dateFrom->month);
                $employers->whereYear('created_at', $dateFrom->year)
                    ->whereMonth('created_at', $dateFrom->month);
                $jobs->whereYear('created_at', $dateFrom->year)
                    ->whereMonth('created_at', $dateFrom->month);
            }

            if (str_contains($periodInterval, 'day')) {
                $employees->whereYear('created_at', $dateFrom->year)
                    ->whereDate('created_at', $dateFrom);
                $employers->whereYear('created_at', $dateFrom->year)
                    ->whereDate('created_at', $dateFrom);
                $jobs->whereYear('created_at', $dateFrom->year)
                    ->whereDate('created_at', $dateFrom);
            }

            $totalCandidates = $employees->where(function(Builder $query){
//                $query->where('role', User::$ROLE_EMPLOYEE)
//                    ->orWhereNull('role');
                $query->where('role', User::$ROLE_EMPLOYEE);
            })->count();

            $result[] = [
                'date' => $dateFrom->toJSON(),
                'candidates' => $totalCandidates,
                'employers' => $employers->where('role', User::$ROLE_EMPLOYER)->count(),
                'jobs' => $jobs->count(),
            ];
            $dateFrom->add('1 ' . $periodInterval);
        }

        return $result;
    }
}
