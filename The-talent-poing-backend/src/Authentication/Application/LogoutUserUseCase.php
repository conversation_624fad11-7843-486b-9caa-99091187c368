<?php

namespace Src\Authentication\Application;

use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;

class LogoutUserUseCase
{

    public function __construct()
    {
    }


    /**
     * @throws Exception
     */
    public function __invoke(): void
    {
        if (Auth::check()) {
            $authUser = (new User)->find(Auth::id());
            $authUser->tokens()->delete();
        } else {
            throw new Exception('No authenticated user on this session');
        }
    }
}
