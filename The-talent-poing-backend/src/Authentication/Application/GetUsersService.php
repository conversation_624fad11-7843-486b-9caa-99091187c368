<?php

namespace Src\Authentication\Application;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Src\Authentication\Domain\User;

class GetUsersService
{
    /**
     * @throws Exception
     */
    public function __invoke(): Collection
    {
        $users = (new User())->all();
        return $users;
    }
}
