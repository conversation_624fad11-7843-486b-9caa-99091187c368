<?php

namespace Src\Authentication\Application;

use Src\Accounts\Application\DeleteTenantService;
use Src\Authentication\Domain\User;

class DeleteUserService
{
    /**
     * __invoke
     *
     * @param  User $user
     * @return void
     */
    public function __invoke(User $user)
    {
        $accounts = $user->profile->accounts;
        foreach ($accounts as $account) {
            // (new DeleteTenantService())($account);
            $account->delete();
        }
        $user->delete();
        return $user;
    }
}
