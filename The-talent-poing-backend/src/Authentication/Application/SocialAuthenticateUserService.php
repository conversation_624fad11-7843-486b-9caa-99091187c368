<?php

namespace Src\Authentication\Application;

use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Mail\RegistrationMail;
use Illuminate\Http\UploadedFile;
use Src\FileManagement\Application\CreateFileService;

class SocialAuthenticateUserService
{
    public function __construct()
    {
    }

    /**
     * @throws Exception
     */
    public function __invoke(string $email, string $name, string $token, string $provider = 'social_provider', string $image = null): string
    {
        $user = (new User)->where('email', $email)->first();

        if (!$user) {

            $user = new User();
            $user->name = $name;
//            $user->profile_image = $image;
            $user->email = $email;
            $user->status = 'active';
            $user->email_verified_at = Carbon::now();
            if (str_contains($image, 'google')) {
//                $user->google_id = $token;
            }
            $user->save();
            Mail::to($user->email)
                ->bcc(config('mail.from.address'))
                ->send(new RegistrationMail($user->name));
        }
        //if (!$user->profile_image) {
//            $user->profile_image = $image;
//            $user->save();
        //

        if ($image && (!$user->profile_image || str_contains($image, "media.licdn.com"))) {
            if (filter_var($image, FILTER_VALIDATE_URL))
            {
                $imageContents = file_get_contents($image);

                if ($imageContents !== false) {
                    $tmpPath = storage_path('app/tmp_profile_pic_' . time());
                    file_put_contents($tmpPath, $imageContents);

                    $extension = pathinfo(parse_url($image, PHP_URL_PATH), PATHINFO_EXTENSION);
                    if (!in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'svg', 'webp'])) {
                        $extension = 'jpg';
                    }

                    $uploadedFile = new UploadedFile(
                        $tmpPath,
                        'profile.' . $extension,
                        mime_content_type($tmpPath),
                        null,
                        true
                    );

                    $file = (new CreateFileService($user))($uploadedFile, true, true);

                    $user->update(['profile_image' => $file->uuid]);

                    unlink($tmpPath);
                }
            }
        }

        if (str_contains($image, "media.licdn.com") && $user->company)
        {
            $company = $user->company;

            $company->fk_logo_file_uuid = $user->profile_image;

            $company->save();
        }

        $this->ensureUserIsEnabled($user);
        return $user->createToken($provider)->plainTextToken;
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserIsEnabled(User $user): void
    {
        if ($user->status !== 'active') {
            throw new Exception('This user is not active');
        }
    }
}
