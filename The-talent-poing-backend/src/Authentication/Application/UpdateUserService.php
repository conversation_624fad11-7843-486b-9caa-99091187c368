<?php

namespace Src\Authentication\Application;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Src\Accounts\Domain\Account;

class UpdateUserService
{
    /**
     * __invoke
     *
     * @param array $data
     * @param Account $account
     * @return void
     */
    public function __invoke(array $data, Account $account): void
    {
        $user = $account->ownerProfile->user;
        if ($data['password']) {
            $data['temp_password'] = Crypt::encrypt($data['password']);
            $data['password'] = Hash::make($data['password']);
        }
        $user->update($data);
    }
}
