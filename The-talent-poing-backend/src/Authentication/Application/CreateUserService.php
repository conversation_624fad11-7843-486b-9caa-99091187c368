<?php

namespace Src\Authentication\Application;

use Exception;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Src\Accounts\Domain\Profile;
use Src\Authentication\Domain\User;

class CreateUserService
{
    /**
     * @throws Exception
     */
    public function __invoke(Profile $profile, string $email, string $password, bool $isAdmin = false): User
    {
        $user = (new User())->create([
            'name' => $profile->name,
            'email' => $email,
            'password' => Hash::make($password),
            'temp_password' => Crypt::encrypt($password),
            'is_admin' => $isAdmin,
        ]);
        $user->save();
        $profile->fk_user_uuid = $user->uuid;
        $profile->save();
        return $user;
    }
}
