<?php

namespace Src\Authentication\Application;

use Exception;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;
use Src\Authentication\Domain\User;

class CheckSessionFromTokenID
{
    public function __construct()
    {
    }

    /**
     * @param  string  $tokenID
     * @return User
     * @throws Exception
     */
    public function __invoke(string $tokenID): User
    {
        $accessToken = PersonalAccessToken::where('token', $tokenID)->first();
        $user = $accessToken->tokenable;
        if (!$user) {
            throw new Exception('Can found any user');
        }
        return $user;
    }
}
