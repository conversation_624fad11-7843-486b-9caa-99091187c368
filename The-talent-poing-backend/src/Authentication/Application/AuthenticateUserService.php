<?php

namespace Src\Authentication\Application;

use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;

class AuthenticateUserService
{
    public function __construct()
    {
    }

    /**
     * @throws Exception
     */
    public function __invoke(string $email, string $password, string $clientName = 'api'): string
    {
        if (Auth::attempt(['email' => $email, 'password' => $password])) {
            $user = (new User)->where('email', $email)->first();
            $this->ensureUserIsEnabled($user);
            return $user->createToken($clientName)->plainTextToken;
        } else {
            throw new Exception('The credentials are not correct');
        }
    }

    /**
     *
     * @throws Exception
     */
    public function ensureUserIsEnabled(User $user): void
    {
        if ($user->status !== 'active') {
            throw new Exception('This user is not active');
        }
    }
}
