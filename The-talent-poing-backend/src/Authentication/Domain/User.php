<?php

namespace Src\Authentication\Domain;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Database\Factories\Src\Authentication\Domain\UserFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\PersonalAccessToken;
use Src\Accounts\Domain\Profile;
use Src\Authentication\Traits\HasAuthDevices;

/**
 * Src\Authentication\Domain\User
 *
 * @property string $uuid
 * @property string|null $name
 * @property string $email
 * @property Carbon|null $email_verified_at
 * @property string|null $disabled_at
 * @property string $password
 * @property string|null $remember_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection|PersonalAccessToken[] $tokens
 * @property-read int|null $tokens_count
 * @method static UserFactory factory(...$parameters)
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User query()
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDisabledAt($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereName($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUuid($value)
 * @mixin Eloquent
 * @mixin Builder
 * @property string $temp_password
 * @property string|null $otp_code
 * @method static Builder|User whereOtpCode($value)
 * @method static Builder|User whereTempPassword($value)
 * @property-read Profile|null $profile
 * @property string|null $code_expire_at
 * @method static Builder|User whereCodeExpireAt($value)
 * @property int $is_admin
 * @method static Builder|User whereIsAdmin($value)
 * @property-read Collection|\Src\Authentication\Domain\AuthDevice[] $authDevices
 * @property-read int|null $auth_devices_count
 */
class User extends Authenticatable
{

    use HasApiTokens, HasFactory, Notifiable, HasAuthDevices;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'temp_password',
        'otp_code',
        'email_verified_at',
        'code_expire_at',
        'is_admin'
    ];
    protected $keyType = 'string';
    protected $primaryKey = 'uuid';
    public $incrementing = false;

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_admin' => 'boolean',
    ];

    public static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $model->uuid = Str::uuid()->toString();
        });
    }

    public function profile(): HasOne|Profile
    {
        return $this->hasOne(Profile::class, 'fk_user_uuid', 'uuid');
    }
}
