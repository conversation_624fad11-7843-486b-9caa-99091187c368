<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MigrationController;
use Src\FileManagement\Infrastructure\Controllers\FilesController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::get('/', function () {
    return view('welcome');
});

Route::get('/reset-user', function () {
    $user = \App\Models\User::find(1);
    $user->password = \Illuminate\Support\Facades\Hash::make('12345678');
    $user->save();
    return view('welcome');
});

Route::get('/stats', function () {
    $res = (new \Src\EmployerManagement\Application\GetAdminDashboardStats())(4, 'days');
    dd($res);
    return view('welcome');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/getmigration -data', [MigrationController::class, 'getMigration']);

Route::middleware(['web'])->prefix('storage')->group(function () {
    Route::get('/files/{uuid}/view', [FilesController::class, 'view'])
        ->name('file-view');
    Route::get('/files/{uuid}/thumbnail', [FilesController::class, 'thumbnail'])
        ->name('file-thumbnail');
    Route::get('/{any}', function () {
        // This will be handled by your middleware
    })->where('any', '.*');
});
