<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserCandidateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $candidates = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'current_position' => 'Senior Software Developer',
                'years_of_experience' => '5-7',
                'current_salary' => '75000',
                'desired_salary' => '85000',
                'bio' => 'Passionate full-stack developer with expertise in React, Node.js, and cloud technologies.',
                'gender' => 'male',
                'date_of_birth' => '1990-03-15',
                'where_currently_based' => '1',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'current_position' => 'Digital Marketing Manager',
                'years_of_experience' => '3-5',
                'current_salary' => '55000',
                'desired_salary' => '65000',
                'bio' => 'Creative marketing professional with proven track record in social media and content marketing.',
                'gender' => 'female',
                'date_of_birth' => '1992-07-22',
                'where_currently_based' => '2',
                'job_type' => 'fulltime',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'Michael Chen',
                'email' => '<EMAIL>',
                'current_position' => 'Data Scientist',
                'years_of_experience' => '2-3',
                'current_salary' => '70000',
                'desired_salary' => '80000',
                'bio' => 'Data enthusiast with strong background in machine learning and statistical analysis.',
                'gender' => 'male',
                'date_of_birth' => '1994-11-08',
                'where_currently_based' => '3',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'current_position' => 'UX/UI Designer',
                'years_of_experience' => '4-6',
                'current_salary' => '60000',
                'desired_salary' => '70000',
                'bio' => 'User-centered designer passionate about creating intuitive and beautiful digital experiences.',
                'gender' => 'female',
                'date_of_birth' => '1991-05-12',
                'where_currently_based' => '4',
                'job_type' => 'fulltime',
                'job_status' => 'not_looking',
            ],
            [
                'name' => 'David Rodriguez',
                'email' => '<EMAIL>',
                'current_position' => 'Project Manager',
                'years_of_experience' => '7-10',
                'current_salary' => '80000',
                'desired_salary' => '90000',
                'bio' => 'Experienced project manager with expertise in Agile methodologies and team leadership.',
                'gender' => 'male',
                'date_of_birth' => '1987-09-30',
                'where_currently_based' => '5',
                'job_type' => 'fulltime',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'Jessica Thompson',
                'email' => '<EMAIL>',
                'current_position' => 'Financial Analyst',
                'years_of_experience' => '2-3',
                'current_salary' => '50000',
                'desired_salary' => '60000',
                'bio' => 'Detail-oriented financial professional with strong analytical and problem-solving skills.',
                'gender' => 'female',
                'date_of_birth' => '1995-01-18',
                'where_currently_based' => '6',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Robert Kim',
                'email' => '<EMAIL>',
                'current_position' => 'DevOps Engineer',
                'years_of_experience' => '4-6',
                'current_salary' => '85000',
                'desired_salary' => '95000',
                'bio' => 'Cloud infrastructure specialist with expertise in AWS, Docker, and Kubernetes.',
                'gender' => 'male',
                'date_of_birth' => '1989-12-03',
                'where_currently_based' => '7',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Amanda Wilson',
                'email' => '<EMAIL>',
                'current_position' => 'Content Writer',
                'years_of_experience' => '1-2',
                'current_salary' => '40000',
                'desired_salary' => '50000',
                'bio' => 'Creative writer with passion for storytelling and digital content creation.',
                'gender' => 'female',
                'date_of_birth' => '1996-04-25',
                'where_currently_based' => '8',
                'job_type' => 'parttime',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'James Anderson',
                'email' => '<EMAIL>',
                'current_position' => 'Sales Manager',
                'years_of_experience' => '6-8',
                'current_salary' => '70000',
                'desired_salary' => '80000',
                'bio' => 'Results-driven sales professional with proven track record in B2B sales.',
                'gender' => 'male',
                'date_of_birth' => '1988-08-14',
                'where_currently_based' => '9',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Lisa Garcia',
                'email' => '<EMAIL>',
                'current_position' => 'HR Specialist',
                'years_of_experience' => '3-5',
                'current_salary' => '55000',
                'desired_salary' => '65000',
                'bio' => 'People-focused HR professional with expertise in recruitment and employee relations.',
                'gender' => 'female',
                'date_of_birth' => '1993-06-07',
                'where_currently_based' => '10',
                'job_type' => 'fulltime',
                'job_status' => 'not_looking',
            ],
            [
                'name' => 'Kevin Brown',
                'email' => '<EMAIL>',
                'current_position' => 'Mobile App Developer',
                'years_of_experience' => '2-3',
                'current_salary' => '65000',
                'desired_salary' => '75000',
                'bio' => 'Mobile development specialist with experience in iOS and Android platforms.',
                'gender' => 'male',
                'date_of_birth' => '1994-10-19',
                'where_currently_based' => '1',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Rachel Martinez',
                'email' => '<EMAIL>',
                'current_position' => 'Graphic Designer',
                'years_of_experience' => '4-6',
                'current_salary' => '50000',
                'desired_salary' => '60000',
                'bio' => 'Creative visual designer with expertise in branding and print design.',
                'gender' => 'female',
                'date_of_birth' => '1991-02-28',
                'where_currently_based' => '2',
                'job_type' => 'contract',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'Thomas Lee',
                'email' => '<EMAIL>',
                'current_position' => 'Network Administrator',
                'years_of_experience' => '5-7',
                'current_salary' => '60000',
                'desired_salary' => '70000',
                'bio' => 'IT infrastructure specialist with strong background in network security.',
                'gender' => 'male',
                'date_of_birth' => '1990-11-16',
                'where_currently_based' => '3',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Nicole Taylor',
                'email' => '<EMAIL>',
                'current_position' => 'Business Analyst',
                'years_of_experience' => '3-5',
                'current_salary' => '65000',
                'desired_salary' => '75000',
                'bio' => 'Strategic thinker with expertise in process improvement and data analysis.',
                'gender' => 'female',
                'date_of_birth' => '1992-09-05',
                'where_currently_based' => '4',
                'job_type' => 'fulltime',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'Christopher White',
                'email' => '<EMAIL>',
                'current_position' => 'Quality Assurance Engineer',
                'years_of_experience' => '2-3',
                'current_salary' => '55000',
                'desired_salary' => '65000',
                'bio' => 'Detail-oriented QA professional with expertise in automated testing.',
                'gender' => 'male',
                'date_of_birth' => '1995-07-11',
                'where_currently_based' => '5',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Stephanie Clark',
                'email' => '<EMAIL>',
                'current_position' => 'Social Media Manager',
                'years_of_experience' => '1-2',
                'current_salary' => '45000',
                'desired_salary' => '55000',
                'bio' => 'Social media enthusiast with creative approach to brand engagement.',
                'gender' => 'female',
                'date_of_birth' => '1997-03-20',
                'where_currently_based' => '6',
                'job_type' => 'parttime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Daniel Harris',
                'email' => '<EMAIL>',
                'current_position' => 'Database Administrator',
                'years_of_experience' => '6-8',
                'current_salary' => '75000',
                'desired_salary' => '85000',
                'bio' => 'Database expert with experience in MySQL, PostgreSQL, and MongoDB.',
                'gender' => 'male',
                'date_of_birth' => '1988-12-09',
                'where_currently_based' => '7',
                'job_type' => 'fulltime',
                'job_status' => 'not_looking',
            ],
            [
                'name' => 'Michelle Lewis',
                'email' => '<EMAIL>',
                'current_position' => 'Product Manager',
                'years_of_experience' => '4-6',
                'current_salary' => '80000',
                'desired_salary' => '90000',
                'bio' => 'Product strategy expert with focus on user experience and market research.',
                'gender' => 'female',
                'date_of_birth' => '1990-05-17',
                'where_currently_based' => '8',
                'job_type' => 'fulltime',
                'job_status' => 'open_to_offer',
            ],
            [
                'name' => 'Andrew Walker',
                'email' => '<EMAIL>',
                'current_position' => 'Cybersecurity Analyst',
                'years_of_experience' => '3-5',
                'current_salary' => '70000',
                'desired_salary' => '80000',
                'bio' => 'Security professional with expertise in threat analysis and incident response.',
                'gender' => 'male',
                'date_of_birth' => '1993-01-24',
                'where_currently_based' => '9',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
            [
                'name' => 'Jennifer Hall',
                'email' => '<EMAIL>',
                'current_position' => 'Marketing Coordinator',
                'years_of_experience' => '1-2',
                'current_salary' => '42000',
                'desired_salary' => '52000',
                'bio' => 'Marketing professional with strong organizational skills and creative mindset.',
                'gender' => 'female',
                'date_of_birth' => '1996-08-13',
                'where_currently_based' => '10',
                'job_type' => 'fulltime',
                'job_status' => 'ready_to_interview',
            ],
        ];

        foreach ($candidates as $index => $candidateData) {
            $user = User::create([
                'name' => $candidateData['name'],
                'email' => $candidateData['email'],
                'slug' => Str::slug($candidateData['name']),
                'password' => bcrypt('password123'),
                'view_password' => 'password123',
                'role' => 'employee',
                'company_id' => null,
                'available_resume_count' => 5,
                'profile_image' => null,
                'where_job_search' => $candidateData['where_currently_based'],
                'job_type' => $candidateData['job_type'],
                'job_status' => $candidateData['job_status'],
                'where_currently_based' => $candidateData['where_currently_based'],
                'current_position' => $candidateData['current_position'],
                'profile_complete_percentage' => rand(60, 95),
                'unlock_instant_apply' => rand(0, 1),
                'showcontact_no' => rand(0, 1),
                'date_of_birth' => $candidateData['date_of_birth'],
                'gender' => $candidateData['gender'],
                'years_of_experience' => $candidateData['years_of_experience'],
                'current_salary' => $candidateData['current_salary'],
                'desired_salary' => $candidateData['desired_salary'],
                'bio' => $candidateData['bio'],
                'currency' => 'USD',
                'industry' => '1',
                'sector' => '1',
                'skills' => '1,2,3', // Will be updated by triggers
                'facebook_link' => 'https://facebook.com/' . Str::slug($candidateData['name']),
                'twitter_link' => 'https://twitter.com/' . Str::slug($candidateData['name']),
                'linkedin_link' => 'https://linkedin.com/in/' . Str::slug($candidateData['name']),
                'email_verified_at' => Carbon::now(),
                'contact_no' => '+1-555-' . str_pad($index + 3000, 4, '0', STR_PAD_LEFT),
                'login_count' => rand(1, 25),
                'first_login' => 0,
                'is_approved' => '1',
                'status' => 'active',
                'created_at' => Carbon::now()->subDays(rand(30, 365)),
                'updated_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
        }
    }
}
