<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TeamMembersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all employer users with companies
        $employers = User::where('role', 'employer')
            ->whereHas('company')
            ->with('company')
            ->get();

        $positions = [
            'HR Manager', 'Recruiter', 'HR Assistant', 'Talent Acquisition Specialist',
            'HR Business Partner', 'People Operations Manager', 'HR Coordinator',
            'Recruitment Consultant', 'HR Generalist', 'Employee Relations Specialist'
        ];

        $firstNames = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
        ];

        $lastNames = [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
            '<PERSON>', '<PERSON>', '<PERSON>', 'Taylor', '<PERSON>', '<PERSON>', '<PERSON>'
        ];

        foreach ($employers as $employer) {
            // Random number of team members (0-5)
            $teamMemberCount = rand(0, 5);
            
            for ($i = 0; $i < $teamMemberCount; $i++) {
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $fullName = $firstName . ' ' . $lastName;
                $email = strtolower($firstName . '.' . $lastName . '@' . 
                    str_replace(' ', '', strtolower($employer->company->company_name)) . '.com');

                User::create([
                    'name' => $fullName,
                    'email' => $email,
                    'slug' => Str::slug($fullName),
                    'password' => bcrypt('password123'),
                    'view_password' => 'password123',
                    'role' => 'staff',
                    'company_id' => $employer->company->id,
                    'created_by_id' => $employer->id, // Link to employer who created them
                    'current_position' => $positions[array_rand($positions)],
                    'job_status' => 'ready_to_interview',
                    'profile_complete_percentage' => rand(60, 90),
                    'contact_no' => '+1-555-' . str_pad(rand(4000, 9999), 4, '0', STR_PAD_LEFT),
                    'email_verified_at' => Carbon::now(),
                    'is_approved' => '1',
                    'status' => 'active',
                    'created_at' => Carbon::now()->subDays(rand(1, 180)),
                    'updated_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
}
