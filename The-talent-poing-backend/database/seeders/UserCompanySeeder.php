<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserCompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $companies = [
            [
                'name' => 'TechCorp Solutions',
                'email' => '<EMAIL>',
                'company_name' => 'TechCorp Solutions',
                'company_website' => 'https://www.techcorp.com',
                'company_sector' => '1', // IT sector
                'company_location' => '1', // Country ID
                'no_of_employees' => '51-100',
                'company_description' => 'Leading software development company specializing in web and mobile applications.',
                'designation' => 'HR Manager',
                'linkedin_link' => 'https://linkedin.com/company/techcorp',
            ],
            [
                'name' => 'Digital Marketing Pro',
                'email' => '<EMAIL>',
                'company_name' => 'Digital Marketing Pro',
                'company_website' => 'https://www.digitalmarketingpro.com',
                'company_sector' => '2', // Marketing sector
                'company_location' => '2',
                'no_of_employees' => '11-50',
                'company_description' => 'Full-service digital marketing agency helping businesses grow online.',
                'designation' => 'Talent Acquisition Specialist',
                'linkedin_link' => 'https://linkedin.com/company/digitalmarketingpro',
            ],
            [
                'name' => 'FinanceFirst Bank',
                'email' => '<EMAIL>',
                'company_name' => 'FinanceFirst Bank',
                'company_website' => 'https://www.financefirst.com',
                'company_sector' => '3', // Finance sector
                'company_location' => '3',
                'no_of_employees' => '201-500',
                'company_description' => 'Modern banking solutions with a focus on customer experience and innovation.',
                'designation' => 'Head of Human Resources',
                'linkedin_link' => 'https://linkedin.com/company/financefirst',
            ],
            [
                'name' => 'HealthCare Plus',
                'email' => '<EMAIL>',
                'company_name' => 'HealthCare Plus',
                'company_website' => 'https://www.healthcareplus.com',
                'company_sector' => '4', // Healthcare sector
                'company_location' => '4',
                'no_of_employees' => '101-200',
                'company_description' => 'Comprehensive healthcare services with state-of-the-art medical facilities.',
                'designation' => 'HR Director',
                'linkedin_link' => 'https://linkedin.com/company/healthcareplus',
            ],
            [
                'name' => 'EduTech Innovations',
                'email' => '<EMAIL>',
                'company_name' => 'EduTech Innovations',
                'company_website' => 'https://www.edutech.com',
                'company_sector' => '5', // Education sector
                'company_location' => '5',
                'no_of_employees' => '51-100',
                'company_description' => 'Revolutionary educational technology solutions for modern learning.',
                'designation' => 'People Operations Manager',
                'linkedin_link' => 'https://linkedin.com/company/edutech',
            ],
            [
                'name' => 'GreenEnergy Corp',
                'email' => '<EMAIL>',
                'company_name' => 'GreenEnergy Corp',
                'company_website' => 'https://www.greenenergy.com',
                'company_sector' => '6', // Energy sector
                'company_location' => '6',
                'no_of_employees' => '501-1000',
                'company_description' => 'Sustainable energy solutions for a cleaner future.',
                'designation' => 'Chief People Officer',
                'linkedin_link' => 'https://linkedin.com/company/greenenergy',
            ],
            [
                'name' => 'RetailMax Chain',
                'email' => '<EMAIL>',
                'company_name' => 'RetailMax Chain',
                'company_website' => 'https://www.retailmax.com',
                'company_sector' => '7', // Retail sector
                'company_location' => '7',
                'no_of_employees' => '1000+',
                'company_description' => 'Leading retail chain with stores across multiple countries.',
                'designation' => 'Regional HR Manager',
                'linkedin_link' => 'https://linkedin.com/company/retailmax',
            ],
            [
                'name' => 'LogiTrans Solutions',
                'email' => '<EMAIL>',
                'company_name' => 'LogiTrans Solutions',
                'company_website' => 'https://www.logitrans.com',
                'company_sector' => '8', // Logistics sector
                'company_location' => '8',
                'no_of_employees' => '201-500',
                'company_description' => 'Global logistics and transportation solutions provider.',
                'designation' => 'HR Business Partner',
                'linkedin_link' => 'https://linkedin.com/company/logitrans',
            ],
            [
                'name' => 'CreativeStudio Agency',
                'email' => '<EMAIL>',
                'company_name' => 'CreativeStudio Agency',
                'company_website' => 'https://www.creativestudio.com',
                'company_sector' => '9', // Creative sector
                'company_location' => '9',
                'no_of_employees' => '11-50',
                'company_description' => 'Award-winning creative agency specializing in branding and design.',
                'designation' => 'Creative Director & HR',
                'linkedin_link' => 'https://linkedin.com/company/creativestudio',
            ],
            [
                'name' => 'ManufacturePro Industries',
                'email' => '<EMAIL>',
                'company_name' => 'ManufacturePro Industries',
                'company_website' => 'https://www.manufacturepro.com',
                'company_sector' => '10', // Manufacturing sector
                'company_location' => '10',
                'no_of_employees' => '501-1000',
                'company_description' => 'Advanced manufacturing solutions with cutting-edge technology.',
                'designation' => 'VP of Human Resources',
                'linkedin_link' => 'https://linkedin.com/company/manufacturepro',
            ],
        ];

        foreach ($companies as $index => $companyData) {
            // Create employer user with minimal required fields
            $user = User::create([
                'name' => $companyData['name'] . ' HR Team',
                'email' => $companyData['email'],
                'password' => bcrypt('password123'),
                'role' => 'employer',
                'job_status' => 'ready_to_interview', // Valid enum: ready_to_interview, open_to_offer, not_looking
                'is_approved' => '1', // Valid enum: '0', '1', '2'
                'status' => 'active', // Valid enum: pending, active, deleted, deactive
            ]);

            // Update with additional fields after successful creation
            $user->update([
                'slug' => Str::slug($companyData['name'] . '-hr-team'),
                'view_password' => 'password123',
                'available_resume_count' => 100,
                'profile_complete_percentage' => 85,
                'unlock_instant_apply' => 1,
                'contact_no' => '+1-555-' . str_pad($index + 1000, 4, '0', STR_PAD_LEFT),
                'login_count' => rand(5, 50),
            ]);

            // Create company
            $company = Company::create([
                'user_id' => $user->id,
                'company_name' => $companyData['company_name'],
                'company_slug' => Str::slug($companyData['company_name']),
                'company_email' => $companyData['email'],
                'designation' => $companyData['designation'],
                'company_website' => $companyData['company_website'],
                'company_location' => $companyData['company_location'],
                'company_sector' => $companyData['company_sector'],
                'no_of_employees' => $companyData['no_of_employees'],
                'company_description' => $companyData['company_description'],
                'company_contact_no' => '+1-555-' . str_pad($index + 2000, 4, '0', STR_PAD_LEFT),
                'available_resume_count' => 100,
                'linkedin_link' => $companyData['linkedin_link'],
                'twitter_link' => 'https://twitter.com/' . Str::slug($companyData['company_name']),
                'facebook_link' => 'https://facebook.com/' . Str::slug($companyData['company_name']),
                'instagram_link' => 'https://instagram.com/' . Str::slug($companyData['company_name']),
                'meta_tag' => $companyData['company_name'] . ' Careers - The Talent Point',
                'meta_desc' => 'Join ' . $companyData['company_name'] . ' and advance your career.',
                'status' => 'active',
            ]);

            // Update user's company_id
            $user->update(['company_id' => $company->id]);
        }
    }
}
