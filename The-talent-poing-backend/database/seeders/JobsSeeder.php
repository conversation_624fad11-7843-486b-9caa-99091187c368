<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Job;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Str;
use Carbon\Carbon;

class JobsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all employer users with companies
        $employers = User::where('role', 'employer')
            ->whereHas('company')
            ->with('company')
            ->get();

        $jobTitles = [
            'Software Engineer', 'Senior Developer', 'Frontend Developer', 'Backend Developer',
            'Full Stack Developer', 'DevOps Engineer', 'Data Scientist', 'Product Manager',
            'UX/UI Designer', 'Marketing Manager', 'Sales Representative', 'Business Analyst',
            'Project Manager', 'Quality Assurance Engineer', 'Database Administrator',
            'System Administrator', 'Network Engineer', 'Cybersecurity Analyst',
            'Digital Marketing Specialist', 'Content Writer', 'Graphic Designer',
            'Customer Success Manager', 'Account Manager', 'HR Specialist'
        ];

        $jobTypes = ['fulltime', 'parttime', 'contract', 'freelance'];
        $jobStatuses = ['active', 'closed', 'expired', 'deleted', 'deactive']; // Valid enum values
        $experienceLevels = ['0-1', '1-2', '2-3', '3-5', '5-7', '7-10', '10+'];
        $positionTypes = ['remote', 'onsite', 'hybrid'];

        $jobDescriptions = [
            'We are looking for a talented professional to join our dynamic team. The ideal candidate will have strong technical skills and excellent communication abilities.',
            'Join our innovative company and work on cutting-edge projects. We offer competitive salary, great benefits, and opportunities for professional growth.',
            'Seeking an experienced professional to lead our team and drive business results. Must have proven track record and leadership experience.',
            'Exciting opportunity to work with the latest technologies in a fast-paced environment. Perfect for someone who loves challenges and continuous learning.',
            'We are expanding our team and looking for passionate individuals who want to make a difference. Great company culture and work-life balance.',
        ];

        foreach ($employers as $employer) {
            // Random number of jobs per company (0-8)
            $jobCount = rand(0, 8);
            
            for ($i = 0; $i < $jobCount; $i++) {
                $jobTitle = $jobTitles[array_rand($jobTitles)];
                $minSalary = rand(30000, 80000);
                $maxSalary = $minSalary + rand(10000, 40000);
                $deadline = Carbon::now()->addDays(rand(7, 90));

                // Create job
                Job::create([
                    'user_id' => $employer->id,
                    'company_id' => $employer->company->id,
                    'sector_id' => $employer->company->company_sector ?: 1,
                    'job_title' => $jobTitle,
                    'job_slug' => Str::slug($jobTitle . '-' . $employer->company->company_name),
                    'job_description' => $jobDescriptions[array_rand($jobDescriptions)],
                    'type_of_position' => $positionTypes[array_rand($positionTypes)],
                    'job_country' => $employer->company->company_location ?: 1,
                    'job_city' => rand(1, 10), // Assuming cities 1-10 exist
                    'industry' => rand(1, 10), // Assuming industries 1-10 exist
                    'experience' => $experienceLevels[array_rand($experienceLevels)],
                    'skills_required' => implode(',', array_slice(range(1, 20), 0, rand(3, 7))),
                    'monthly_fixed_salary_currency' => 'USD',
                    'monthly_fixed_salary_min' => (string)$minSalary,
                    'monthly_fixed_salary_max' => (string)$maxSalary,
                    'available_vacancies' => (string)rand(1, 5),
                    'deadline' => $deadline->format('Y-m-d'),
                    'is_featured' => rand(0, 1),
                    'hide_employer_details' => rand(0, 1),
                    'meta_tag' => $jobTitle . ' - ' . $employer->company->company_name,
                    'meta_desc' => 'Apply for ' . $jobTitle . ' position at ' . $employer->company->company_name . '. Great opportunity for career growth.',
                    'job_type' => $jobTypes[array_rand($jobTypes)],
                    'job_status' => $jobStatuses[array_rand($jobStatuses)],
                    'postal_code' => str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT),
                    'street_address' => rand(100, 9999) . ' ' . ['Main St', 'Oak Ave', 'Park Blvd', 'First St', 'Second Ave'][array_rand(['Main St', 'Oak Ave', 'Park Blvd', 'First St', 'Second Ave'])],
                    'created_at' => Carbon::now()->subDays(rand(1, 365)),
                    'updated_at' => Carbon::now()->subDays(rand(1, 30)),
                ]);
            }
        }
    }
}
