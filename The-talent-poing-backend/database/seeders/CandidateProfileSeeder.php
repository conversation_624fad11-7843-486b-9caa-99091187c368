<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Education;
use App\Models\WorkExperience;
use App\Models\EmployeeSkills;
use App\Models\Language;
use App\Models\Portfolio;
use Carbon\Carbon;

class CandidateProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get all employee users
        $employees = User::where('role', 'employee')->get();

        $universities = [
            'Harvard University', 'Stanford University', 'MIT', 'University of California Berkeley',
            'Yale University', 'Princeton University', 'Columbia University', 'University of Chicago',
            'University of Pennsylvania', 'Cornell University', 'New York University', 'Boston University'
        ];

        $degrees = [
            'Bachelor of Computer Science', 'Bachelor of Engineering', 'Bachelor of Business Administration',
            'Master of Computer Science', 'Master of Business Administration', 'Bachelor of Arts',
            'Bachelor of Science', 'Master of Engineering', 'Bachelor of Information Technology',
            'Master of Data Science', 'Bachelor of Marketing', 'Master of Finance'
        ];

        $companies = [
            'Google', 'Microsoft', 'Apple', 'Amazon', 'Facebook', 'Netflix', 'Tesla', 'Uber',
            'Airbnb', 'Spotify', 'Adobe', 'Salesforce', 'Oracle', 'IBM', 'Intel', 'Cisco'
        ];

        $jobTitles = [
            'Software Engineer', 'Senior Developer', 'Frontend Developer', 'Backend Developer',
            'Full Stack Developer', 'Data Analyst', 'Product Manager', 'UX Designer',
            'Marketing Specialist', 'Sales Representative', 'Project Manager', 'Business Analyst'
        ];

        $skills = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // Assuming skills with IDs 1-10 exist

        $languages = [
            ['language' => 'English', 'proficiency' => 'Native'],
            ['language' => 'Spanish', 'proficiency' => 'Fluent'],
            ['language' => 'French', 'proficiency' => 'Intermediate'],
            ['language' => 'German', 'proficiency' => 'Basic'],
            ['language' => 'Chinese', 'proficiency' => 'Intermediate'],
            ['language' => 'Japanese', 'proficiency' => 'Basic'],
        ];

        foreach ($employees as $employee) {
            // Add Education (1-2 records per employee)
            $educationCount = rand(1, 2);
            for ($i = 0; $i < $educationCount; $i++) {
                $startYear = rand(2010, 2018);
                $endYear = $startYear + rand(2, 4);
                
                Education::create([
                    'user_id' => $employee->id,
                    'education_title' => $universities[array_rand($universities)],
                    'degree' => $degrees[array_rand($degrees)],
                    'start_date' => $startYear,
                    'end_date' => $endYear,
                    'currently_study_here' => $i == 0 && rand(0, 10) < 2 ? 'yes' : 'no',
                    'your_score' => rand(75, 95),
                    'max_score' => 100,
                    'status' => 'active',
                    'created_at' => $employee->created_at,
                    'updated_at' => $employee->updated_at,
                ]);
            }

            // Add Work Experience (1-3 records per employee)
            $experienceCount = rand(1, 3);
            for ($i = 0; $i < $experienceCount; $i++) {
                $startDate = Carbon::now()->subYears(rand(1, 8))->format('Y-m');
                $endDate = $i == 0 && rand(0, 10) < 3 ? null : 
                    Carbon::parse($startDate)->addYears(rand(1, 3))->format('Y-m');
                
                WorkExperience::create([
                    'user_id' => $employee->id,
                    'title' => $jobTitles[array_rand($jobTitles)],
                    'company' => $companies[array_rand($companies)],
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'currently_work_here' => $endDate === null ? 'yes' : 'no',
                    'description' => $this->generateJobDescription(),
                    'status' => 'active',
                    'created_at' => $employee->created_at,
                    'updated_at' => $employee->updated_at,
                ]);
            }

            // Add Skills (3-7 skills per employee)
            $employeeSkills = array_rand($skills, rand(3, 7));
            if (!is_array($employeeSkills)) {
                $employeeSkills = [$employeeSkills];
            }
            
            foreach ($employeeSkills as $skillIndex) {
                EmployeeSkills::create([
                    'user_id' => $employee->id,
                    'skill_id' => $skills[$skillIndex],
                    'status' => 'active',
                    'created_at' => $employee->created_at,
                    'updated_at' => $employee->updated_at,
                ]);
            }

            // Add Languages (1-3 languages per employee)
            $languageCount = rand(1, 3);
            $selectedLanguages = array_rand($languages, $languageCount);
            if (!is_array($selectedLanguages)) {
                $selectedLanguages = [$selectedLanguages];
            }
            
            foreach ($selectedLanguages as $langIndex) {
                Language::create([
                    'user_id' => $employee->id,
                    'language' => $languages[$langIndex]['language'],
                    'proficiency' => $languages[$langIndex]['proficiency'],
                    'status' => 'active',
                    'created_at' => $employee->created_at,
                    'updated_at' => $employee->updated_at,
                ]);
            }

            // Add Portfolio (0-2 projects per employee)
            if (rand(0, 10) < 7) { // 70% chance to have portfolio
                $portfolioCount = rand(1, 2);
                for ($i = 0; $i < $portfolioCount; $i++) {
                    $startDate = Carbon::now()->subMonths(rand(6, 36))->format('Y-m');
                    $endDate = rand(0, 10) < 3 ? null : 
                        Carbon::parse($startDate)->addMonths(rand(3, 12))->format('Y-m');
                    
                    Portfolio::create([
                        'user_id' => $employee->id,
                        'title' => $this->generateProjectTitle(),
                        'portfolio_link' => 'https://github.com/' . strtolower(str_replace(' ', '', $employee->name)) . '/project-' . ($i + 1),
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'present' => $endDate === null ? 'yes' : 'no',
                        'description' => $this->generateProjectDescription(),
                        'status' => 'active',
                        'created_at' => $employee->created_at,
                        'updated_at' => $employee->updated_at,
                    ]);
                }
            }
        }
    }

    private function generateJobDescription()
    {
        $descriptions = [
            'Developed and maintained web applications using modern frameworks and technologies.',
            'Collaborated with cross-functional teams to deliver high-quality software solutions.',
            'Implemented responsive user interfaces and optimized application performance.',
            'Participated in code reviews and maintained coding standards across the team.',
            'Worked with databases to design efficient data models and optimize queries.',
            'Led project initiatives and mentored junior developers.',
            'Analyzed business requirements and translated them into technical specifications.',
            'Implemented automated testing strategies to ensure code quality.',
            'Managed client relationships and provided technical consultation.',
            'Contributed to system architecture decisions and technology stack selection.',
        ];
        
        return $descriptions[array_rand($descriptions)];
    }

    private function generateProjectTitle()
    {
        $adjectives = ['Modern', 'Advanced', 'Innovative', 'Smart', 'Dynamic', 'Responsive'];
        $nouns = ['E-commerce Platform', 'Task Management App', 'Social Media Dashboard', 
                 'Analytics Tool', 'Mobile Application', 'Web Portal', 'API Service', 'CRM System'];
        
        return $adjectives[array_rand($adjectives)] . ' ' . $nouns[array_rand($nouns)];
    }

    private function generateProjectDescription()
    {
        $descriptions = [
            'A comprehensive web application built with modern technologies and best practices.',
            'Full-stack project featuring responsive design and real-time functionality.',
            'Mobile-first application with intuitive user interface and seamless user experience.',
            'Data-driven platform with advanced analytics and reporting capabilities.',
            'Scalable solution designed to handle high traffic and complex business logic.',
            'Cross-platform application with robust backend API and modern frontend.',
        ];
        
        return $descriptions[array_rand($descriptions)];
    }
}
