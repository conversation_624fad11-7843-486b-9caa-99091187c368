<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Skills;
use Carbon\Carbon;

class Skillsseeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Programming languages',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Web development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Database management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Cloud computing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Cybersecurity',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=> 'Data analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Artificial Intelligence',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Mobile app development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'DevOps',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'UI/UX design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Network administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Software testing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Big Data',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Internet of Things',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Project management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Technical support',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Blockchain technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Machine learning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Robotics process automation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Data visualization',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Cloud architecture design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'User interface',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'User experience',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Network security',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'1',
            'Skills'=>'Business intelligence',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        
        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medical diagnosis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Treatment planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Patient care',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medical records management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Surgical skills',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Laboratory testing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medical imaging',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medication administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Emergency response',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Infection control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Healthcare technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Patient education',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Rehabilitation therapies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medical research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Telemedicine',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Health informatics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Disease prevention',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Health promotion',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Palliative care',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Health assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Medical ethics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Interdisciplinary collaboration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Healthcare policy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Health data analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'2',
            'Skills' => 'Quality improvement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Financial analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Risk management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Investment strategies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Financial modeling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Banking operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Asset management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Budgeting and forecasting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Credit analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Wealth management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Regulatory compliance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Corporate finance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Retail banking',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Financial planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Capital markets',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Treasury management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Loan processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Financial reporting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Fraud prevention',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Market research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Foreign exchange ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Portfolio management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Mergers and acquisitions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Derivatives trading',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Personal finance advising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'3',
            'Skills' => 'Auditing and internal controls',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Curriculum development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Classroom management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Teaching methodologies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Assessment and evaluation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational technology ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Learning theories',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Pedagogical research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Student counseling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Special education',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'E-learning & online education',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational psychology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Teacher training & development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Education policy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational leadership',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Academic advising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Student engagement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Education administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational equity & inclusion',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Curriculum alignment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Classroom differentiation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Student motivation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Educational program evaluation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'4',
            'Skills' => 'Higher education administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Structural analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Civil engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Mechanical engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Electrical engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Construction management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Project planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Building design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Architecture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Materials science',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Geotechnical engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Environmental engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Surveying and mapping',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Computer-Aided Design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Construction safety',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'HVAC',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Structural engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Plumbing engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Traffic engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Water resources engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Construction estimating',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Construction scheduling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Quality control in construction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Construction inspection',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Building codes & regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'5',
            'Skills' => 'Green building & sustainability',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Market research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Brand management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Advertising campaigns',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Digital marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Social media marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Content marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Search engine optimization ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Pay-per-click ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Email marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Marketing analytics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Customer segmentation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Consumer behavior analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Marketing strategy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Public relations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Promotional events',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Creative design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Copywriting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Media planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Influencer marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Customer relationship management ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Product positioning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Marketing automation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Market trends analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Competitive analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'6',
            'Skills' => 'Brand identity development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Inventory management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Merchandising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Point-of-sale systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Customer service',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Supply chain management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'E-commerce platforms',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Online payment systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Order fulfillment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Product pricing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Retail sales strategies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Consumer trends analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Retail analytics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Customer loyalty programs',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Visual merchandising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Omni-channel retailing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Product catalog management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Product packaging and labeling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Dropshipping and logistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Returns and exchanges handling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Affiliate marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Online advertising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'E-commerce SEO',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Customer reviews and ratings',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Website optimization',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'7',
            'Skills' => 'Cross-selling and upselling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Customer service',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hotel management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Travel planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Tour guiding',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Food and beverage services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Event management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Reservation systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Front office operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Tourism promotion',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Culinary arts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality finance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Cultural awareness',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Destination management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Guest relations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Travel agency operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Resort management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality software',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Responsible tourism',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Conference and convention planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Hospitality entrepreneurship',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Revenue management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'8',
            'Skills' => 'Language skills',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Production planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Quality control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Lean manufacturing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Six Sigma',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Supply chain management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Process optimization',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Assembly line operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Inventory management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Material handling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Manufacturing automation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Machine operation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Product development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Workforce management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Production scheduling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Safety protocols',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Root cause analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Equipment maintenance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Continuous improvement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Production efficiency monitoring',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Just-in-time manufacturing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Productive Maintenance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Statistical process control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Manufacturing cost analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Waste reduction and recycling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'9',
            'Skills' => 'Industry 4.0 technologies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Route planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Freight forwarding',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Supply chain management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Warehousing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Transportation logistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Fleet management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Freight',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Import',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Last-mile delivery',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Inventory management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Transportation scheduling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Logistics technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Logistics network design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Export',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Reverse logistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Cross-docking operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Freight consolidation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Load optimization',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Intermodal transportation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Freight brokerage',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Freight rate negotiation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Port operations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Supply chain visibility',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Cargo security',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'10',
            'Skills' => 'Transportation cost analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Solar energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Wind energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Biomass energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Hydropower',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Geothermal energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Energy efficiency',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Renewable energy policy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Green technologies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Sustainable development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Carbon footprint reduction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Climate change mitigation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Environmental conservation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Waste-to-energy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Clean transportation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Smart grid systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Energy storage technologies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Off-grid renewable systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Environmental impact assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Eco-friendly construction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
          
        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Greenhouse gas emissions monitoring',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
          
        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Renewable energy financing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
          
        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Biodiversity protection',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
          
        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Energy auditing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
          
        Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Environmental regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
         Skills::create( [
            'sector_id'=>'11',
            'Skills' => 'Green certifications',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Mobile networks',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Fixed-line networks',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Broadband internet',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Satellite communications',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Fiber optic technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Wireless communication',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => '5G technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Voice over Internet Protocol',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Data transmission',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Network infrastructure',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication equipment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telephony services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Internet Service Providers',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication standards',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Network security',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication protocols',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Cloud-based communication',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Video conferencing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Unified Communications',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication regulatory frameworks',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication towers and antennas',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Remote communication technologies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

         Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'IoT connectivity',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication billing and invoicing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'12',
            'Skills' => 'Telecommunication project management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Television production',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
         Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Film production',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Digital media creation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Broadcasting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Journalism',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Content creation and curation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Social media management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Video editing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Visual effects',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Music production',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Live event production',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Public relations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Cinematography',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Scriptwriting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Talent management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media law and intellectual',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media ethics and standards',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media distribution',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Video streaming platforms',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Podcasting and audio',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Interactive media and gaming',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Film directing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media research and trend',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

          Skills::create( [
            'sector_id'=>'13',
            'Skills' => 'Media convergence and multi-platform strategies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate sales',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
                 Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate investments',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Residential properties',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Commercial properties',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate appraisal',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Rental property',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property inspection',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate finance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create([
            'sector_id'=>'14',
            'Skills' => 'Property listings',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate brokerage',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property leasing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property valuation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate investment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate negotiation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Land development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Commercial real estate ',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Residential property',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate transactions',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property tax assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate asset management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Real estate contracts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'14',
            'Skills' => 'Property taxes',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Case analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Contract drafting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Litigation support',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal counseling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Court representation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal documentation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Dispute resolution',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Case management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Corporate law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Employment law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Family law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Criminal defense',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Real estate law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Immigration law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Tax law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Environmental law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Client counseling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Arbitration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal compliance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Bankruptcy and insolvency',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Legal advocacy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Estate planning and probate',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'15',
            'Skills' => 'Civil law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Fundraising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Grant writing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Program development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Community outreach',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Volunteer coordination',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Advocacy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Social work',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Counseling services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Non-profit management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Strategic planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Needs assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Impact evaluation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Capacity building',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Donor relations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Resource mobilization',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Non-profit governance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Social service delivery',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Crisis intervention',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Volunteer recruitment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Public policy analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Mental health services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Client support services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Food and nutrition programs',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Education and tutoring services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'16',
            'Skills' => 'Social advocacy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Business consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Management advisory',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Financial consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Strategy development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Market research and analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Technology consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Human resources advisory',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Organizational development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Process improvement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Change management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Rik assessment and management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'IT consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Marketing and branding consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Legal advisory',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Supply chain consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Sustainability consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Performance improvement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Risk management advisory',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Tax consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Energy consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Human resources consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Education consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Healthcare consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Real estate consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'17',
            'Skills' => 'Non-profit consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Aerospace',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Defense',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Avionics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Space',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Missiles',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'UAVs',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Radar',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Satellites',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Aeronautics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Composites',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Propulsion',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Cybersecurity',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Aircraft',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Spacecraft',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Ballistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Surveillance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Innovation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Communication',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Logistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Testing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Materials',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Contracts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
         
        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'18',
            'Skills' => 'Technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Biotechnology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Pharmaceuticals',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Drug development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Biomedical research',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Genetic engineering',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Clinical trials',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Vaccine development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Molecular biology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Bioinformatics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Drug manufacturing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Pharmacology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Cell culture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Gene therapy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Personalized medicine',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Pharmaceutical regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Biochemistry',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Immunology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Therapeutic proteins',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Pharmaceutical formulation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Bioprocessing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Genomics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Drug delivery systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Quality control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Biomarkers',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                 Skills::create( [
            'sector_id'=>'19',
            'Skills' => 'Drug safety assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agriculture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

                Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Crop cultivation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Livestock farming',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Irrigation systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Sustainable agriculture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Pest management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Soil health and fertility',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agricultural machinery',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agribusiness',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Food processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Food safety and quality',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Food packaging',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Food preservation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Food supply chain management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Farm management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agricultural biotechnology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Precision agriculture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Aquaculture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Horticulture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Dairy processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Meat processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Grain milling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agricultural economics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Agricultural policy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'20',
            'Skills' => 'Farm-to-table practices',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Public policy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Government administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

               Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Budgeting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Legislation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Leadership',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Policy analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Ethics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Communications',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Political science',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Governance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Decision-making',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Accountability',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Procurement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Innovation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Information systems',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Personnel management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Advocacy',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Polling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Reform',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Relations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Evaluation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Engagement',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Community development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'21',
            'Skills' => 'Conflict resolution',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Cardiovascular endurance',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Flexibility',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Speed training',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Agility',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Coordination',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports nutrition',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Injury prevention',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports psychology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports coaching',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Athletic conditioning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Plyometric training',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Balance training',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Core strength',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Functional fitness',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Cross-training',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports rehabilitation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports medicine',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports biomechanics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Endurance training',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sport-specific drills',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Recovery techniques',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Strength and power development',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports performance analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Stretching techniques',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'22',
            'Skills' => 'Sports event planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Apparel manufacturing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion merchandising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Pattern making',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Garment construction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion illustration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Textile technology',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Trend forecasting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion styling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Retail buying',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion branding',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fabric sourcing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Sustainable fashion',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Quality control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion photography',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion journalism',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion modeling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion e-commerce',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion consulting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion retail',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Clothing alterations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion show',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion PR',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'23',
            'Skills' => 'Fashion accessory',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Visual arts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Graphic design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Fine arts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Illustration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Photography',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Digital art',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Sculpture',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Painting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Printmaking',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Typography',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Animation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Product design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Interior design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Industrial design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'User experience design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Fashion design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Web design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Motion graphics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Illustrative storytelling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Concept art',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Ceramic art',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Textile design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Exhibit design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => 'Art direction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'24',
            'Skills' => '3D modeling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance policies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk assessment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Underwriting',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Actuarial analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Claims processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk mitigation',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance regulations',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Loss control',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance coverage',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance claims',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk financing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance marketing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk transfer',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance brokerage',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk analysis',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance premiums',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance underwriters',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk modeling',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance contracts',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk exposure',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance adjusters',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk management strategies',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance law',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Risk monitoring',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Skills::create( [
            'sector_id'=>'25',
            'Skills' => 'Insurance planning',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

    }
}