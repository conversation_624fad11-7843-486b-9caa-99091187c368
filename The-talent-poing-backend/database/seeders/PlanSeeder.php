<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */

    public function run()
    {

        DB::table('plans')->truncate();

        DB::table('plans')->insert([
            'plan_title' => 'Free Plan',
            'plan_sub_desc' => 'Basic plan with limited features',
            'plan_currency' => 'AED',
            'plan_amount' => 0,
            'plan_type' => 'Month',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('plans')->insert([
            'plan_title' => '12-Month Plan',
            'plan_sub_desc' => 'Premium plan for 12 months',
            'plan_currency' => 'AED',
            'plan_amount' => 2500,
            'plan_type' => 'Month',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);


        DB::table('plans')->insert([
            'plan_title' => '18-Month Plan',
            'plan_sub_desc' => 'Premium plan for 18 months',
            'plan_currency' => 'AED',
            'plan_amount' => 3500,
            'plan_type' => 'Month',
            'status' => 'active',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

}
