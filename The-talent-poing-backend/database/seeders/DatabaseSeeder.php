<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {

        DB::table('countries')->truncate();
        DB::table('skills')->truncate();
        DB::table('sector')->truncate();

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('users')->truncate();
        DB::table('file_activities')->truncate(); // If you want to truncate this as well
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        DB::table('industries')->truncate();
        DB::table('admin_settings')->truncate();
        DB::table('blog_categories')->truncate();
        DB::table('blogs')->truncate();
        DB::table('default_experience')->truncate();

        DB::table('jobs')->truncate();
        DB::table('job_country_faqs')->truncate();
        DB::table('company')->truncate();
        DB::table('default_experience')->truncate();
        DB::table('data_points')->truncate();

        $this->call(AdminUserSeeder::class);
        $this->call(SkillsSeeder::class);
        $this->call(SectorSeeder::class);
        $this->call(CountriesSeeder::class);
        //$this->call(EmployeeSeeder::class);
        //$this->call(EmployerSeeder::class);

        // New comprehensive seeders
        $this->call(UserCompanySeeder::class);
        $this->call(UserCandidateSeeder::class);
        $this->call(CandidateProfileSeeder::class);
        $this->call(TeamMembersSeeder::class);
        $this->call(JobsSeeder::class);
        $this->call(CitiesSeeder::class);

        $this->call(IndustriesTableSeeder::class);
        $this->call(AdminSettingsTableSeeder::class);
        $this->call(BlogCategorySeeder::class);
        $this->call(BlogSeeder::class);
        $this->call(ExperianceSeeder::class);
        $this->call(LocationFaqSeeder::class);
        $this->call(DataPointSeeder::class);
        $this->call(JobCountryFAQSeeder::class);
    }
}
