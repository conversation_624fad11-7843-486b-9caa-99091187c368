# Database Seeders for The Talent Point

## New Seeders for Test Data

### UserCompanySeeder
Creates **10 companies** with complete profiles:
- Users with `employer` role
- Complete company profiles with different sectors
- Realistic data (names, websites, descriptions, contacts)
- Social media and meta data

### UserCandidateSeeder  
Creates **20 candidates** with basic profiles:
- Users with `employee` role
- Various positions and experience levels
- Realistic salary expectations
- Different job search statuses

### CandidateProfileSeeder
Completes candidate profiles:
- **Education** (1-2 records per candidate)
- **Work experience** (1-3 records per candidate)  
- **Skills** (3-7 skills per candidate)
- **Languages** (1-3 languages per candidate)
- **Portfolio** (0-2 projects per candidate)

## Usage

### Testing (recommended first):
```bash
# Test seeders with 1 company and 1 candidate
php artisan db:seed --class=TestUserCompanySeeder
php artisan db:seed --class=TestUserCandidateSeeder
```

### Run all seeders:
```bash
php artisan db:seed
```

### Run individual seeders:
```bash
# Companies only
php artisan db:seed --class=UserCompanySeeder

# Candidates only
php artisan db:seed --class=UserCandidateSeeder

# Candidate profiles only
php artisan db:seed --class=CandidateProfileSeeder
```

## Prerequisites

Before running, ensure the following exist:

1. **Countries** (`countries` table)
2. **Sectors** (`sector` table) 
3. **Skills** (`skills` table)
4. **Admin settings** (`admin_settings` table)

This data is created by existing seeders:
- `CountriesSeeder`
- `SectorSeeder` 
- `SkillsSeeder`
- `AdminSettingsTableSeeder`

## Data Structure

### Companies:
- **Sectors**: IT, Marketing, Finance, Healthcare, Education, Energy, Retail, Logistics, Creative, Manufacturing
- **Sizes**: from 11-50 to 1000+ employees
- **Locations**: various countries (ID 1-10)

### Candidates:
- **Positions**: Software Developer, Marketing Manager, Data Scientist, UX Designer, Project Manager, and others
- **Experience**: from 0-1 to 7-10+ years
- **Statuses**: ready_to_interview, open_to_offer, not_looking
- **Job types**: fulltime, parttime, contract

### Candidate Profiles:
- **Universities**: Harvard, Stanford, MIT, Berkeley, and others
- **Companies**: Google, Microsoft, Apple, Amazon, Facebook, and others
- **Languages**: English, Spanish, French, German, Chinese, Japanese
- **Proficiency levels**: Native, Fluent, Intermediate, Basic

## Features

1. **Triggers**: System will automatically create records in `settings` and `notifications` via DB triggers
2. **Relationships**: All table relationships are properly configured
3. **Realistic data**: Uses real company names, universities, skills
4. **Diversity**: Data covers different industries, experience levels, locations

## Troubleshooting

### "Data truncated for column" Error
If you get data truncation errors:
1. Check if required reference tables exist (countries, sector, skills)
2. Run test seeders first for diagnostics
3. Verify correct enum values in fields

### Foreign Key Error
If foreign key errors occur:
```bash
# Run basic seeders first
php artisan db:seed --class=CountriesSeeder
php artisan db:seed --class=SectorSeeder
php artisan db:seed --class=SkillsSeeder
php artisan db:seed --class=AdminSettingsTableSeeder
```

## Passwords

All test users have password: `password123`

## Notes

- Seeders use existing Laravel models
- Data is created considering model fillable fields
- All records have 'active' status
- Creation dates are distributed over time for realism

## Enum Fields and Values

### Users:
- **user.role**: 'superadmin', 'admin', 'employee', 'employer', 'staff'
- **user.job_status**: 'ready_to_interview', 'open_to_offer', 'not_looking'
- **user.is_approved**: '0', '1', '2'
- **user.status**: 'pending', 'active', 'deleted', 'deactive'

### Jobs:
- **jobs.job_type**: 'fulltime', 'parttime', 'contract', 'freelance'
- **jobs.job_status**: 'active', 'closed', 'expired', 'deleted', 'deactive'

### Applications:
- **applications.apply_status**: 'pending', 'under_review', 'reviewed', 'selected', 'declined', 'closed'

### Interviews:
- **interviews.interview_status**: 'pending', 'completed', 'deleted', 'canceled', 'scheduled', 'accepted', 'rejected'