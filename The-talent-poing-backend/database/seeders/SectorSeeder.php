<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Sector;
use Carbon\Carbon;

class SectorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        Sector::create( [
            'id'=>1,
            'sector_name'=>'Information Technology (IT)',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>2,
            'sector_name'=>'Healthcare and Medical Services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>3,
            'sector_name'=>'Finance and Banking',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>4,
            'sector_name'=>'Education and Academia',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>5,
            'sector_name'=>'Engineering and Construction',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Sector::create( [
            'id'=>6,
            'sector_name'=>'Marketing and Advertising',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );


        Sector::create( [
            'id'=>7,
            'sector_name'=>'Retail and E-commerce',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>8,
            'sector_name'=>'Hospitality and Tourism',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>9,
            'sector_name'=>'Manufacturing and Production',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>10,
            'sector_name'=>'Transportation and Logistics',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>11,
            'sector_name'=>'Renewable Energy and Environment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>12,
            'sector_name'=>'Telecommunications',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>13,
            'sector_name'=>'Media and Entertainment',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>14,
            'sector_name'=>'Real Estate and Property',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>15,
            'sector_name'=>'Legal Services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>16,
            'sector_name'=>'Non-profit and Social Services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>17,
            'sector_name'=>'Consulting and Advisory Services',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>18,
            'sector_name'=>'Aerospace and Defense',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
        Sector::create( [
            'id'=>19,
            'sector_name'=>'Biotechnology and Pharmaceuticals',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>20,
            'sector_name'=>'Agriculture and Food Processing',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>21,
            'sector_name'=>'Government and Public Administration',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>22,
            'sector_name'=>'Sports and Fitness',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>23,
            'sector_name'=>'Fashion and Apparel',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>24,
            'sector_name'=>'Art and Design',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );

        Sector::create( [
            'id'=>25,
            'sector_name'=>'Insurance and Risk Management',
            'status'=>'active',
            'created_at'=>Carbon::now(),
            'updated_at'=>Carbon::now()
        ] );
    }
}