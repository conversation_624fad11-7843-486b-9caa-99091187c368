/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `admin_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `favicon` varchar(255) DEFAULT NULL,
  `payment_gateway` enum('stripe') DEFAULT 'stripe',
  `payment_mode` enum('live','testing') DEFAULT 'testing',
  `stripe_test_secret_key` varchar(255) DEFAULT NULL,
  `stripe_test_publish_key` varchar(255) DEFAULT NULL,
  `stripe_live_secret_key` varchar(255) DEFAULT NULL,
  `stripe_live_publish_key` varchar(255) DEFAULT NULL,
  `homepage_meta_title` varchar(255) DEFAULT NULL,
  `homepage_meta_description` varchar(255) DEFAULT NULL,
  `jobs_meta_title` varchar(255) DEFAULT NULL,
  `jobs_meta_description` varchar(255) DEFAULT NULL,
  `carrer_meta_title` varchar(255) DEFAULT NULL,
  `carrer_meta_description` varchar(255) DEFAULT NULL,
  `about_meta_title` varchar(255) DEFAULT NULL,
  `about_meta_description` varchar(255) DEFAULT NULL,
  `employer_meta_title` varchar(255) DEFAULT NULL,
  `employer_meta_description` varchar(255) DEFAULT NULL,
  `employee_meta_title` varchar(255) DEFAULT NULL,
  `employee_meta_description` varchar(255) DEFAULT NULL,
  `pricing_meta_title` varchar(255) DEFAULT NULL,
  `pricing_meta_description` varchar(255) DEFAULT NULL,
  `blog_listing_meta_title` varchar(255) DEFAULT NULL,
  `blog_listing_meta_description` varchar(255) DEFAULT NULL,
  `linkedin_link` varchar(255) DEFAULT NULL,
  `twitter_link` varchar(255) DEFAULT NULL,
  `instagram_link` varchar(255) DEFAULT NULL,
  `facebook_link` varchar(255) DEFAULT NULL,
  `website_url` varchar(255) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `applications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `applications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `job_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `jobpost_by_userId` int(11) DEFAULT NULL,
  `resume_path` varchar(255) DEFAULT NULL,
  `cover_letter` varchar(255) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `apply_status` enum('pending','under_review','reviewed','selected','declined','closed') NOT NULL,
  `hiring_status` enum('Yes','No','Maybe') NOT NULL DEFAULT 'Maybe',
  `instant_apply` int(11) DEFAULT 0,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `applications_user_id_index` (`user_id`),
  KEY `applications_job_id_index` (`job_id`),
  KEY `applications_company_id_index` (`company_id`),
  KEY `applications_jobpost_by_userid_index` (`jobpost_by_userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_application_insert_trigger AFTER INSERT ON applications
FOR EACH ROW
BEGIN
    DECLARE applicant_user_name VARCHAR(255);
    DECLARE jobpost_by_user_id INT;
    DECLARE job_title VARCHAR(255);
    DECLARE company_name VARCHAR(255);
    DECLARE applicant_notification_text VARCHAR(1000);
    DECLARE jobposter_notification_text VARCHAR(1000);
    DECLARE link VARCHAR(255);
    DECLARE has_error BOOLEAN DEFAULT FALSE;
    DECLARE error_msg VARCHAR(255);
    DECLARE line_number INT;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
        SET error_msg = CONCAT("Error in new_application_insert_trigger: ", @errmsg);
        INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_application_insert_trigger", "error");
        SET has_error = TRUE;
    END;    
    -- Get the applicant's user_name from the users table
    SELECT name INTO applicant_user_name FROM users WHERE id = NEW.user_id;

    -- Get the job's jobpost_by_userId from the jobs table
    SELECT user_id INTO jobpost_by_user_id FROM jobs WHERE id = NEW.job_id;

    -- Get the job_title and company_name from the jobs and company tables
    SELECT j.job_title, c.company_name INTO job_title, company_name
    FROM jobs j
    JOIN company c ON NEW.company_id = c.id
    WHERE j.id = NEW.job_id;

    -- Get the link from admin_settings table
    SELECT website_url INTO link FROM admin_settings WHERE id = 1;
    SET link = CONCAT(link, 'employees/messages/applications');

    -- Create notification texts for the applicant and job poster
    SET applicant_notification_text = CONCAT(applicant_user_name, " has applied for the position of ", job_title, " at ", company_name);
    SET jobposter_notification_text = CONCAT("You have received a new application from ", applicant_user_name, " for the position of ", job_title);

    -- Insert a new row into the notifications table for the job poster
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (NEW.user_id, jobpost_by_user_id, jobposter_notification_text, link, false, "interview", "active", NOW(), NOW());

    -- Insert a new row into the notifications table for the applicant
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (NEW.user_id, NEW.user_id, applicant_notification_text, link, false, "interview", "active", NOW(), NOW());
    IF NOT has_error THEN
    INSERT INTO error_logs (error_message, line_number, file_name, type)
    VALUES ("Trigger executed successfully: new_application_insert_trigger", line_number, "new_application_insert_trigger", "success");
    END IF;
    END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_application_status_updated
        AFTER UPDATE ON applications
        FOR EACH ROW
        BEGIN
            DECLARE applicant_user_id INT;
            DECLARE applicant_user_name VARCHAR(255);
            DECLARE company_user_id INT;
            DECLARE job_title VARCHAR(255);
            DECLARE company_name VARCHAR(255);
            DECLARE notification_text VARCHAR(1000);
            DECLARE link VARCHAR(255);
            DECLARE has_error BOOLEAN DEFAULT FALSE;
            DECLARE error_msg VARCHAR(255);
            DECLARE line_number INT;
        
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                SET error_msg = CONCAT("Error in new_application_status_updated: ", @errmsg);
                INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_application_status_updated", "error");
                SET has_error = TRUE;
            END;
    
            -- Get the applicants user_id and user_name from the users table
            SELECT u.id, u.name INTO applicant_user_id, applicant_user_name
            FROM users u
            WHERE u.id = NEW.user_id;
    
            -- Get the company users user_id from the jobs table
            SELECT user_id INTO company_user_id FROM jobs WHERE id = NEW.job_id;
    
            -- Get the job_title and company_name from the jobs and company tables
            SELECT j.job_title, c.company_name INTO job_title, company_name
            FROM jobs j
            JOIN company c ON NEW.company_id = c.id
            WHERE j.id = NEW.job_id;
    
            -- Get the link from admin_settings table
            SELECT website_url INTO link FROM admin_settings WHERE id = 1;
            SET link = CONCAT(link, "employees/messages/applications");
    
            -- Check if the application status was updated and create the notification text
            IF NEW.apply_status <> OLD.apply_status THEN
                SET notification_text = CONCAT(applicant_user_name, " has been ",
                                               NEW.apply_status, " to interview for the position of ", job_title, " at ", company_name);
    
                -- Insert a new row into the notifications table for the company user
                INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
                VALUES (applicant_user_id, company_user_id, notification_text, link, false, "interview", "active", NOW(), NOW());
    
                -- Insert a new row into the notifications table for the applicant
                INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
                VALUES (company_user_id, applicant_user_id, notification_text, link, false, "interview", "active", NOW(), NOW());
            END IF;

            IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: new_interview_details_updated", line_number, "new_interview_details_updated", "success");
            END IF;
        END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `availability`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `availability` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `interview_id` int(11) DEFAULT NULL,
  `availability_date` date DEFAULT NULL,
  `availabel` varchar(255) DEFAULT NULL,
  `from_time` varchar(255) DEFAULT NULL,
  `to_time` varchar(255) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `blog_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blog_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) DEFAULT NULL,
  `status` enum('active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `blogs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blogs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `blog_category_id` bigint(20) unsigned DEFAULT NULL,
  `created_by_id` bigint(20) unsigned DEFAULT NULL,
  `name` text DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `author_name` varchar(255) DEFAULT NULL,
  `author_image` varchar(255) DEFAULT NULL,
  `tag` varchar(255) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `image` longtext DEFAULT NULL,
  `meta_tag` text DEFAULT NULL,
  `meta_desc` text DEFAULT NULL,
  `status` enum('active','deactive','delete') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `after_blog_insert_trigger` AFTER INSERT ON `blogs` FOR EACH ROW BEGIN
    -- Declare local variables
    DECLARE blog_title VARCHAR(255);
    DECLARE slug_name VARCHAR(255);
    DECLARE link VARCHAR(255);

    -- Get the blog_title from the newly inserted row
    SET blog_title = NEW.name;
    SET slug_name = NEW.slug;
    SET link = CONCAT((SELECT `website_url` FROM `admin_settings` WHERE `id` = 1), '/blog/', slug_name);

    -- Insert the notification for all users    
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    SELECT 1, `id`, CONCAT('A new blog post was posted: <a href="', link, '">', blog_title, '</a>.'), link, false, "blog_created", "active", NOW(), NOW()
    FROM `users`;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_blog_insert_trigger AFTER INSERT ON blogs FOR EACH ROW
BEGIN
    -- Declare local variables
    DECLARE blog_title VARCHAR(255);
    DECLARE slug_name VARCHAR(255);
    DECLARE link VARCHAR(255);
    DECLARE admin_id INT;

DECLARE has_error BOOLEAN DEFAULT FALSE;
DECLARE error_msg VARCHAR(255);
DECLARE line_number INT;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
    SET error_msg = CONCAT("Error in new_blog_insert_trigger: ", @errmsg);
    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_blog_insert_trigger", "error");
    SET has_error = TRUE;
END;

    SELECT id INTO admin_id FROM users WHERE role = "admin";
    -- Get the blog_title from the newly inserted row
    SET blog_title = NEW.name;
    SET slug_name = NEW.slug;
    SET link = CONCAT((SELECT `website_url` FROM `admin_settings` WHERE `id` = 1), '/blog/', slug_name);
    -- Insert the notification for all users    
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    SELECT admin_id, `id`, CONCAT('A new blog post was posted: <a href="', link, '">', blog_title, '</a>.'), link, false, "blog_created", "active", NOW(), NOW()
    FROM `users`WHERE status = "active" ;

    IF NOT has_error THEN
    INSERT INTO error_logs (error_message, line_number, file_name, type)
    VALUES ("Trigger executed successfully: new_blog_insert_trigger", line_number, "new_blog_insert_trigger", "success");
END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `candidates_filters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidates_filters` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `section_name` varchar(255) DEFAULT NULL,
  `job_status` varchar(255) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `salary` varchar(255) DEFAULT NULL,
  `experience` varchar(255) DEFAULT NULL,
  `country_id` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `sector` text DEFAULT NULL,
  `job_type` varchar(255) DEFAULT NULL,
  `profile_status` varchar(255) DEFAULT NULL,
  `status` enum('active','deactive','delete') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `country_id` int(11) DEFAULT NULL,
  `city_name` varchar(255) DEFAULT NULL,
  `country_name` varchar(255) DEFAULT NULL,
  `status` enum('active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `company` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `company_slug` varchar(255) DEFAULT NULL,
  `company_email` varchar(255) DEFAULT NULL,
  `designation` varchar(255) DEFAULT NULL,
  `company_website` varchar(255) DEFAULT NULL,
  `company_location` varchar(255) DEFAULT NULL,
  `company_sector` varchar(255) DEFAULT NULL,
  `no_of_employees` varchar(255) DEFAULT NULL,
  `company_description` longtext DEFAULT NULL,
  `company_logo` varchar(255) DEFAULT NULL,
  `company_contact_no` varchar(255) DEFAULT NULL,
  `available_resume_count` int(11) DEFAULT 0,
  `linkedin_link` varchar(255) DEFAULT NULL,
  `twitter_link` varchar(255) DEFAULT NULL,
  `instagram_link` varchar(255) DEFAULT NULL,
  `facebook_link` varchar(255) DEFAULT NULL,
  `background_banner_image` varchar(255) DEFAULT NULL,
  `meta_tag` text DEFAULT NULL,
  `meta_desc` text DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_company_name_index` (`company_name`),
  KEY `company_status_index` (`status`),
  KEY `company_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_user_company_profile_update
                AFTER UPDATE ON company
                FOR EACH ROW
                BEGIN
                DECLARE notification_message VARCHAR(255);
                DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;

                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in after_user_company_profile_update: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_user_company_profile_update", "error");
                    SET has_error = TRUE;
                END;  
                    IF NEW.company_name <> OLD.company_name THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your company name has been successfully updated.", NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.company_email <> OLD.company_email THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your company email has been successfully updated.", NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.designation <> OLD.designation THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your Designation has been successfully updated.", NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;
                    
                    IF NEW.company_location <> OLD.company_location THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your company location has been successfully updated.", NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.background_banner_image <> OLD.background_banner_image THEN
                    INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                    VALUES ("Your company background banner image has been successfully updated.", NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;
                    IF NEW.facebook_link <> OLD.facebook_link THEN
                        SET notification_message = "Your Facebook link has been successfully updated.";
                    ELSEIF NEW.twitter_link <> OLD.twitter_link THEN
                        SET notification_message = "Your Twitter link has been successfully updated.";
                    ELSEIF NEW.instagram_link <> OLD.instagram_link THEN
                        SET notification_message = "Your Instagram link has been successfully updated.";
                    ELSEIF NEW.linkedin_link     <> OLD.linkedin_link THEN
                        SET notification_message = "Your Linkedin link has been successfully updated.";
                    END IF;
                    IF notification_message IS NOT NULL THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES (notification_message, NEW.user_id, NEW.user_id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;
                IF NEW.company_logo <> OLD.company_logo THEN
                        INSERT INTO `notifications`(`notification`, `notify_by`, `notify_to`, `notification_type`, `link`, `is_read`, `status`, `created_at`, `updated_at`)
                        VALUES ("Your company logo has been successfully updated.", NEW.user_id, NEW.user_id, "profile_image_updated", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NOT has_error THEN
                    INSERT INTO error_logs (error_message, line_number, file_name, type)
                    VALUES ("Trigger executed successfully: after_user_company_profile_update", line_number, "after_user_company_profile_update", "success");
                    END IF;  
                END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `company_followers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `company_followers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `company_id` int(11) NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_company_followers_insert
            AFTER INSERT ON company_followers
            FOR EACH ROW
            BEGIN
                
                DECLARE follower_user_id INT;
                DECLARE follower_username VARCHAR(255);
                DECLARE followed_company_id INT;
                DECLARE company_name VARCHAR(255);
                -- Declare variables at the beginning
                DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;
    
                -- Declare variable to store the viewed company_id
                DECLARE viewed_company_id INT;
    
                -- Declare the exit handler for exceptions
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in after_company_followers_insert: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_company_followers_insert", "error");
                    SET has_error = TRUE;
                END; 
                SELECT `id`, `name` INTO follower_user_id, follower_username FROM `users` WHERE `id` = NEW.`user_id`;
                -- Get the followed company_id and company_name from the company table
                SELECT `id`, `company_name` INTO followed_company_id, company_name FROM `company` WHERE `id` = NEW.`company_id`;
                -- Send notification to users whose users.company_id matches company_followers.company_id
                INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
                SELECT NEW.`user_id`, u.`id`, CONCAT(follower_username, " followed your company. You will always get notified when your company posts any job."), NULL, false, 'company_follow', 'active', NOW(), NOW()
                FROM `users` u
                WHERE u.`company_id` = followed_company_id;

                -- Check if there were no errors and log success
                IF NOT has_error THEN
                    INSERT INTO error_logs (error_message, line_number, file_name, type)
                    VALUES ("Trigger executed successfully: after_company_followers_insert", line_number, "after_company_followers_insert", "success");
                END IF; 
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `company_profile_view`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `company_profile_view` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER company_profile_view_trigger
            AFTER INSERT ON company_profile_view
            FOR EACH ROW
            BEGIN
                -- Declare variables at the beginning
                DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;
    
                -- Declare variable to store the viewed company_id
                DECLARE viewed_company_id INT;
    
                -- Declare the exit handler for exceptions
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in company_profile_view_trigger: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "company_profile_view_trigger", "error");
                    SET has_error = TRUE;
                END; 
    
                -- Set the viewed_company_id
                SET viewed_company_id = NEW.company_id;
    
                -- Send notifications to all users whose company_id matches the viewed_company_id
                INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
                SELECT 1, u.id, CONCAT('A new user has viewed your company profile.'), NULL, false, 'company_profile_view', 'active', NOW(), NOW()
                FROM users u
                WHERE u.company_id = viewed_company_id;
    
                -- Check if there were no errors and log success
                IF NOT has_error THEN
                    INSERT INTO error_logs (error_message, line_number, file_name, type)
                    VALUES ("Trigger executed successfully: company_profile_view_trigger", line_number, "company_profile_view_trigger", "success");
                END IF; 
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `countries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `country_name` varchar(255) DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `flag` varchar(255) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `capital` varchar(255) DEFAULT NULL,
  `status` enum('active','deleted','deactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `countries_country_name_index` (`country_name`),
  KEY `countries_status_index` (`status`),
  KEY `countries_id_index` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `default_experience`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `default_experience` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` enum('active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `education`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `education` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `education_title` varchar(255) DEFAULT NULL,
  `degree` varchar(255) DEFAULT NULL,
  `start_date` varchar(255) DEFAULT NULL,
  `end_date` varchar(255) DEFAULT NULL,
  `currently_study_here` varchar(255) DEFAULT NULL,
  `your_score` int(11) DEFAULT NULL,
  `max_score` int(11) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employee_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `skill_id` int(11) NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_new_employee_skills_change
            AFTER INSERT ON employee_skills
            FOR EACH ROW
            BEGIN
                DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;

                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in after_new_employee_skills_change: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_new_employee_skills_change", "error");
                    SET has_error = TRUE;
                END;  
                -- Concatenate all the skills for the user into a comma-separated string
                UPDATE users u
                SET skills = (
                    SELECT GROUP_CONCAT(skills.id SEPARATOR ",")
                    FROM skills
                    INNER JOIN employee_skills ON skills.id = employee_skills.skill_id
                    WHERE employee_skills.user_id = NEW.user_id
                )
                WHERE u.id = NEW.user_id;

                IF NOT has_error THEN
                INSERT INTO error_logs (error_message, line_number, file_name, type)
                VALUES ("Trigger executed successfully: after_new_employee_skills_change", line_number, "after_new_employee_skills_change", "success");
                END IF; 
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_new_skills_update
        AFTER UPDATE ON employee_skills
        FOR EACH ROW
        BEGIN
        DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;

                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in after_new_skills_update: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_new_skills_update", "error");
                    SET has_error = TRUE;
                END;             

            IF NEW.status = "active" THEN
                UPDATE users u
                SET skills = (
                    SELECT GROUP_CONCAT(skills.id SEPARATOR ",")
                    FROM skills
                    INNER JOIN employee_skills ON skills.id = employee_skills.skill_id
                    WHERE employee_skills.user_id = NEW.user_id AND employee_skills.status = "active"
                )
                WHERE u.id = NEW.user_id;
            END IF;

            IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: after_new_skills_update", line_number, "after_new_skills_update", "success");
            END IF; 
        END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `error_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `error_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `error_message` text DEFAULT NULL,
  `line_number` text DEFAULT NULL,
  `file_name` text DEFAULT NULL,
  `browser` text DEFAULT NULL,
  `oprating_system` text DEFAULT NULL,
  `loggedin_id` text DEFAULT NULL,
  `ip_adress` text DEFAULT NULL,
  `type` enum('error','success') DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `errorlog`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `errorlog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `error_message` longtext DEFAULT NULL,
  `line_number` varchar(191) DEFAULT NULL,
  `file_name` varchar(191) DEFAULT NULL,
  `browser` varchar(191) DEFAULT NULL,
  `operating_system` varchar(191) DEFAULT NULL,
  `loggedin_id` int(11) DEFAULT NULL,
  `ip_address` varchar(191) DEFAULT NULL,
  `status` enum('1','0') NOT NULL DEFAULT '1' COMMENT '1=Active,0=Deactive',
  `deleted` enum('1','0') NOT NULL DEFAULT '0' COMMENT '1=Deleted,0=Not Deleted',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `industries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `inquires`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `inquires` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `interviews`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `interviews` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `job_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `applicant_id` int(11) DEFAULT NULL,
  `meeting_link` varchar(255) DEFAULT NULL,
  `interview_schedule_date` date DEFAULT NULL,
  `interview_schedule_from_time` time DEFAULT NULL,
  `interview_schedule_to_time` time DEFAULT NULL,
  `interview_status` enum('pending','completed','deleted','canceled','scheduled','accepted','rejected') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `interview_notification_trigger` AFTER INSERT ON `interviews` FOR EACH ROW BEGIN


    -- Declare variables to store applicant's user_id and user_name
     DECLARE applicant_user_id INT;
    DECLARE applicant_user_name VARCHAR(255);
    DECLARE job_title VARCHAR(255);
    DECLARE company_name VARCHAR(255);
    DECLARE job_poster_user_id INT;
    DECLARE link VARCHAR(255);
     

    SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

    SET link = CONCAT(link, 'employees/messages/interviews');

    -- Get the applicant's user_id and user_name from the users table
    SELECT u.id AS applicant_user_id, u.name AS applicant_user_name
        INTO applicant_user_id, applicant_user_name
        FROM users u
        WHERE u.id = NEW.applicant_id;

    -- Get the job_title and company_name from the jobs and company tables
    SELECT j.job_title, c.company_name INTO job_title, company_name
    FROM jobs j
    JOIN company c ON NEW.company_id = c.id
    WHERE j.id = NEW.job_id;

    -- Get the user_id of the user who posted the job from the jobs table
    SELECT user_id INTO job_poster_user_id FROM jobs WHERE id = NEW.job_id;

    -- Insert a new row into the notifications table for the applicant
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (job_poster_user_id, applicant_user_id, CONCAT('Hello ', applicant_user_name, ', an interview request has been sent to you for the position: "', job_title, '" from ', company_name, ' on ', NEW.interview_schedule_date, ' at ', NEW.interview_schedule_from_time), link, false, 'interview', 'active', NOW(), NOW());

    -- Insert a new row into the notifications table for the user who posted the job
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (job_poster_user_id, job_poster_user_id, CONCAT('An interview schedule has been set with you for the position: "', job_title, '" on ', NEW.interview_schedule_date, ' at ', NEW.interview_schedule_from_time), link, false, 'interview', 'active', NOW(), NOW());
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_interview_notification_trigger
AFTER INSERT ON interviews FOR EACH ROW
BEGIN


    -- Declare variables to store applicant's user_id and user_name
     DECLARE applicant_user_id INT;
    DECLARE applicant_user_name VARCHAR(255);
    DECLARE job_title VARCHAR(255);
    DECLARE company_name VARCHAR(255);
    DECLARE job_poster_user_id INT;
    DECLARE link VARCHAR(255);
     
    DECLARE has_error BOOLEAN DEFAULT FALSE;
        DECLARE error_msg VARCHAR(255);
        DECLARE line_number INT;
    
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
            SET error_msg = CONCAT("Error in new_interview_notification_trigger: ", @errmsg);
            INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_interview_notification_trigger", "error");
            SET has_error = TRUE;
        END;
    SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

    SET link = CONCAT(link, 'employees/messages/interviews');

    -- Get the applicant's user_id and user_name from the users table
    SELECT u.id AS applicant_user_id, u.name AS applicant_user_name
        INTO applicant_user_id, applicant_user_name
        FROM users u
        WHERE u.id = NEW.applicant_id;

    -- Get the job_title and company_name from the jobs and company tables
    SELECT j.job_title, c.company_name INTO job_title, company_name
    FROM jobs j
    JOIN company c ON NEW.company_id = c.id
    WHERE j.id = NEW.job_id;

    -- Get the user_id of the user who posted the job from the jobs table
    SELECT user_id INTO job_poster_user_id FROM jobs WHERE id = NEW.job_id;

    -- Insert a new row into the notifications table for the applicant
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (job_poster_user_id, applicant_user_id, CONCAT('Hello ', applicant_user_name, ', an interview request has been sent to you for the position: "', job_title, '" from ', company_name, ' on ', NEW.interview_schedule_date, ' at ', NEW.interview_schedule_from_time), link, false, 'interview', 'active', NOW(), NOW());

    -- Insert a new row into the notifications table for the user who posted the job
    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (job_poster_user_id, job_poster_user_id, CONCAT('An interview schedule has been set with you for the position: "', job_title, '" on ', NEW.interview_schedule_date, ' at ', NEW.interview_schedule_from_time), link, false, 'interview', 'active', NOW(), NOW());
    
    IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: new_interview_notification_trigger", line_number, "new_interview_notification_trigger", "success");
        END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_interview_details_updated
        AFTER UPDATE ON interviews
        FOR EACH ROW
        BEGIN
            DECLARE applicant_user_name VARCHAR(255);
            DECLARE job_title VARCHAR(255);
            DECLARE company_name VARCHAR(255);
            DECLARE job_poster_user_id INT;
            DECLARE notification_text VARCHAR(1000);
            DECLARE link VARCHAR(255);
            DECLARE has_error BOOLEAN DEFAULT FALSE;
            DECLARE error_msg VARCHAR(255);
            DECLARE line_number INT;
        
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                SET error_msg = CONCAT("Error in new_interview_details_updated: ", @errmsg);
                INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_interview_details_updated", "error");
                SET has_error = TRUE;
            END;
            SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;
            SET link = CONCAT(link, "employees/messages/interviews");
    
            SELECT u.name INTO applicant_user_name
            FROM users u
            WHERE u.id = NEW.applicant_id;
    
            SELECT j.job_title, c.company_name INTO job_title, company_name
            FROM jobs j
            JOIN company c ON NEW.company_id = c.id
            WHERE j.id = NEW.job_id;
    
            SELECT user_id INTO job_poster_user_id FROM jobs WHERE id = NEW.job_id;
    
            -- Check which aspect of interview details was updated and create appropriate notification text
            IF NEW.meeting_link <> OLD.meeting_link THEN
                SET notification_text = CONCAT("The meeting link for your interview for the position '", job_title,
                                               "' at ", company_name, " has been updated. New link: ", NEW.meeting_link);
            ELSEIF NEW.interview_schedule_date <> OLD.interview_schedule_date
                OR NEW.interview_schedule_from_time <> OLD.interview_schedule_from_time
                OR NEW.interview_schedule_to_time <> OLD.interview_schedule_to_time THEN
                SET notification_text = CONCAT("The interview schedule for the position '", job_title,
                                               "' at ", company_name, " has been updated. New schedule: ",
                                               NEW.interview_schedule_date, " from ", NEW.interview_schedule_from_time,
                                               " to ", NEW.interview_schedule_to_time);
            ELSEIF NEW.interview_status <> OLD.interview_status THEN
                SET notification_text = CONCAT("The interview status for the position '", job_title,
                                               "' at ", company_name, " has been updated. New status: ", NEW.interview_status);
            END IF;
    
            -- Insert a new row into the notifications table for the applicant
            INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
            VALUES (job_poster_user_id, NEW.applicant_id, notification_text, link, false, 'interview', 'active', NOW(), NOW());
    
            -- Insert a new row into the notifications table for the user who posted the job
            INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
            VALUES (job_poster_user_id, job_poster_user_id, notification_text, link, false, 'interview', 'active', NOW(), NOW());
        
            IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: new_interview_details_updated", line_number, "new_interview_details_updated", "success");
            END IF;
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `job_filters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `job_filters` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `section_name` varchar(255) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `country_id` varchar(255) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `salary` varchar(255) DEFAULT NULL,
  `experience` varchar(255) DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `sector` text DEFAULT NULL,
  `job_type` varchar(255) DEFAULT NULL,
  `status` enum('active','deactive','delete') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `sector_id` int(11) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `job_slug` varchar(255) DEFAULT NULL,
  `job_description` longtext DEFAULT NULL,
  `type_of_position` varchar(255) DEFAULT NULL,
  `job_country` varchar(255) DEFAULT NULL,
  `industry` varchar(255) DEFAULT NULL,
  `experience` varchar(255) DEFAULT NULL,
  `skills_required` text DEFAULT NULL,
  `monthly_fixed_salary_currency` varchar(255) DEFAULT NULL,
  `monthly_fixed_salary_min` varchar(255) DEFAULT NULL,
  `monthly_fixed_salary_max` varchar(255) DEFAULT NULL,
  `available_vacancies` varchar(255) DEFAULT NULL,
  `deadline` date DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `hide_employer_details` tinyint(1) NOT NULL DEFAULT 0,
  `background_banner_image` varchar(255) DEFAULT NULL,
  `meta_tag` text DEFAULT NULL,
  `meta_desc` text DEFAULT NULL,
  `job_type` enum('fulltime','parttime','contract','freelance') DEFAULT 'fulltime',
  `job_status` enum('active','closed','expired','deleted','deactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `job_city` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_job_title_index` (`job_title`),
  KEY `jobs_job_country_index` (`job_country`),
  KEY `jobs_job_type_index` (`job_type`),
  KEY `jobs_job_status_index` (`job_status`),
  KEY `jobs_user_id_index` (`user_id`),
  KEY `jobs_company_id_index` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs_view`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs_view` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `job_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `languages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `language` varchar(255) DEFAULT NULL,
  `proficiency` varchar(255) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `locations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `location_name` varchar(255) DEFAULT NULL,
  `location_slug` varchar(255) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `membership`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `membership` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `expire_at` datetime DEFAULT NULL,
  `purchase_at` datetime DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `messages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) DEFAULT NULL,
  `receiver_id` int(11) DEFAULT NULL,
  `job_id` int(11) DEFAULT NULL,
  `applicants_id` int(11) DEFAULT NULL,
  `message_title` varchar(255) DEFAULT NULL,
  `message_description` longtext DEFAULT NULL,
  `message_type` varchar(255) DEFAULT NULL,
  `message_status` varchar(255) NOT NULL DEFAULT 'unread',
  `attachment_path` varchar(255) DEFAULT NULL,
  `archived_messages` int(11) DEFAULT 0,
  `status` enum('pending','active','deleted') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `notify_by` int(11) DEFAULT NULL,
  `notify_to` int(11) DEFAULT NULL,
  `notification` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `notification_type` enum('jobs','job_apply','membership','message','interview','register','forget_password','login','resume_view','resume_view_count','job_saved','profile_image_updated','password_updated','company_follow','company_profile_view','birthday','blog_created') NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notify_by_index` (`notify_by`),
  KEY `notifications_notify_to_index` (`notify_to`),
  KEY `notifications_notification_type_index` (`notification_type`),
  KEY `notifications_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `membership_id` int(11) DEFAULT NULL,
  `customer_id` varchar(255) DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `subscription_id` varchar(255) DEFAULT NULL,
  `stripe_plan_id` varchar(255) DEFAULT NULL,
  `stripe_plan_product_id` varchar(255) DEFAULT NULL,
  `card_id` varchar(255) DEFAULT NULL,
  `customer_email` varchar(255) DEFAULT NULL,
  `subscription_status` varchar(255) DEFAULT NULL,
  `current_period_start` varchar(255) DEFAULT NULL,
  `current_period_end` varchar(255) DEFAULT NULL,
  `card_holder_name` varchar(255) DEFAULT NULL,
  `card_number` varchar(255) DEFAULT NULL,
  `card_exp_month` int(11) DEFAULT NULL,
  `card_exp_year` int(11) DEFAULT NULL,
  `card_cvv` int(11) DEFAULT NULL,
  `card_type` varchar(255) DEFAULT NULL,
  `amount` varchar(255) DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `payment_gateway` varchar(255) DEFAULT NULL,
  `plan_type` varchar(255) DEFAULT NULL,
  `charge_id` varchar(255) DEFAULT NULL,
  `charge_receipt_url` text DEFAULT NULL,
  `charge_created_date` varchar(255) DEFAULT NULL,
  `expire_at` datetime DEFAULT NULL,
  `purchase_at` datetime DEFAULT NULL,
  `status` enum('pending','active','deleted') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `plans`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `plans` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_title` varchar(255) DEFAULT NULL,
  `plan_sub_desc` longtext DEFAULT NULL,
  `plan_currency` varchar(255) DEFAULT NULL,
  `plan_amount` int(11) DEFAULT NULL,
  `plan_type` enum('Month','Year') DEFAULT 'Month',
  `plan_points` longtext DEFAULT NULL,
  `status` enum('pending','active','deleted') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `portfolio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `portfolio` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `portfolio_link` varchar(255) DEFAULT NULL,
  `start_date` varchar(255) DEFAULT NULL,
  `end_date` varchar(255) DEFAULT NULL,
  `present` varchar(255) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `resumes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `resumes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `resume_pdf_path` varchar(255) NOT NULL,
  `default_resume` tinyint(1) NOT NULL DEFAULT 0,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `resumes_viewed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `resumes_viewed` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `applicant_id` int(11) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `saved_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `saved_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `job_id` int(11) DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `after_saved_jobs_insert` AFTER INSERT ON `saved_jobs` FOR EACH ROW BEGIN
    -- Declare variables
    DECLARE saved_job_title VARCHAR(255);
    DECLARE link VARCHAR(255);

    -- Get the job_title from the jobs table using NEW.job_id
    SELECT `job_title` INTO saved_job_title FROM `jobs` WHERE `id` = NEW.job_id;

    SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

    SET link = CONCAT(link, 'employees/jobs/savedjobs');

    -- Insert the first notification for the user who saved the job
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    VALUES (NEW.user_id, NEW.user_id, CONCAT("You saved the job """, saved_job_title, """."), link, false, "job_saved", "active", NOW(), NOW());

    -- Insert the second notification for the user who posted the job
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    SELECT NEW.user_id, `user_id`, CONCAT("Your job """, saved_job_title, """ has been saved by a user."), link, false, "job_saved", "active", NOW(), NOW()
    FROM `jobs` WHERE `id` = NEW.job_id;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_saved_new_jobs_insert AFTER INSERT ON saved_jobs FOR EACH ROW
BEGIN
    -- Declare variables
    DECLARE saved_job_title VARCHAR(255);
    DECLARE link VARCHAR(255);

DECLARE has_error BOOLEAN DEFAULT FALSE;
DECLARE error_msg VARCHAR(255);
DECLARE line_number INT;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
    SET error_msg = CONCAT("Error in after_saved_new_jobs_insert: ", @errmsg);
    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_saved_new_jobs_insert", "error");
    SET has_error = TRUE;
END;


    -- Get the job_title from the jobs table using NEW.job_id
    SELECT `job_title` INTO saved_job_title FROM `jobs` WHERE `id` = NEW.job_id;

    SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

    SET link = CONCAT(link, 'employees/jobs/savedjobs');

    -- Insert the first notification for the user who saved the job
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    VALUES (NEW.user_id, NEW.user_id, CONCAT("You saved the job \"", saved_job_title, "\"."), link, false, "job_saved", "active", NOW(), NOW());

    -- Insert the second notification for the user who posted the job
    INSERT INTO `notifications` (`notify_by`, `notify_to`, `notification`, `link`, `is_read`, `notification_type`, `status`, `created_at`, `updated_at`)
    SELECT NEW.user_id, `user_id`, CONCAT("Your job \"", saved_job_title, "\" has been saved by a user."), link, false, "job_saved", "active", NOW(), NOW()
    FROM `jobs` WHERE `id` = NEW.job_id;
    IF NOT has_error THEN
    INSERT INTO error_logs (error_message, line_number, file_name, type)
    VALUES ("Trigger executed successfully: after_saved_new_jobs_insert", line_number, "after_saved_new_jobs_insert", "success");
END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `sector`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sector` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sector_name` varchar(255) NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `sector_sector_name_index` (`sector_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `account_access` tinyint(1) NOT NULL DEFAULT 1,
  `newsletter_access` tinyint(1) NOT NULL DEFAULT 1,
  `recommendations_access` tinyint(1) NOT NULL DEFAULT 1,
  `announcements_access` tinyint(1) NOT NULL DEFAULT 1,
  `message_from_candidate_access` tinyint(1) NOT NULL DEFAULT 1,
  `display_contact_no` tinyint(1) NOT NULL DEFAULT 1,
  `display_email_address` tinyint(1) NOT NULL DEFAULT 1,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sector_id` int(11) NOT NULL,
  `skills` varchar(255) NOT NULL,
  `status` enum('pending','active','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `skills_sector_id_index` (`sector_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`sequence`),
  UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  KEY `telescope_entries_batch_id_index` (`batch_id`),
  KEY `telescope_entries_family_hash_index` (`family_hash`),
  KEY `telescope_entries_created_at_index` (`created_at`),
  KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_entries_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL,
  KEY `telescope_entries_tags_entry_uuid_tag_index` (`entry_uuid`,`tag`),
  KEY `telescope_entries_tags_tag_index` (`tag`),
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `telescope_monitoring`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `isShowEmail` tinyint(1) NOT NULL DEFAULT 0,
  `password` varchar(255) DEFAULT NULL,
  `view_password` varchar(255) DEFAULT NULL,
  `role` enum('superadmin','admin','employee','employer','staff') DEFAULT NULL,
  `company_id` int(11) DEFAULT NULL,
  `available_resume_count` int(11) DEFAULT 0,
  `profile_image` varchar(255) DEFAULT NULL,
  `where_job_search` varchar(255) DEFAULT NULL,
  `job_type` varchar(255) DEFAULT NULL,
  `job_status` enum('ready_to_interview','open_to_offer','not_looking') NOT NULL DEFAULT 'ready_to_interview',
  `where_currently_based` varchar(255) DEFAULT NULL,
  `current_position` varchar(255) DEFAULT NULL,
  `profile_complete_percentage` int(11) DEFAULT NULL,
  `unlock_instant_apply` tinyint(1) NOT NULL DEFAULT 0,
  `linked_id` varchar(255) DEFAULT NULL,
  `google_id` varchar(255) DEFAULT NULL,
  `showcontact_no` tinyint(1) NOT NULL DEFAULT 0,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `years_of_experience` varchar(255) DEFAULT NULL,
  `current_salary` varchar(255) DEFAULT NULL,
  `desired_salary` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `currency` varchar(255) DEFAULT NULL,
  `industry` varchar(255) DEFAULT NULL,
  `sector` varchar(255) DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `facebook_link` varchar(255) DEFAULT NULL,
  `twitter_link` varchar(255) DEFAULT NULL,
  `instagram_link` varchar(255) DEFAULT NULL,
  `website_url` varchar(255) DEFAULT NULL,
  `linkedin_link` varchar(255) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `contact_no` varchar(255) DEFAULT NULL,
  `login_count` int(11) DEFAULT NULL,
  `first_login` tinyint(1) NOT NULL DEFAULT 0,
  `created_by_id` int(11) DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `is2FA` tinyint(1) NOT NULL DEFAULT 1,
  `otp` varchar(255) DEFAULT NULL,
  `card_number` varchar(255) DEFAULT NULL,
  `card_exp_month` varchar(255) DEFAULT NULL,
  `card_exp_year` varchar(255) DEFAULT NULL,
  `card_cvv` varchar(255) DEFAULT NULL,
  `card_type` varchar(255) DEFAULT NULL,
  `is_approved` enum('0','1','2') NOT NULL DEFAULT '1',
  `status` enum('pending','active','deleted','deactive') NOT NULL DEFAULT 'active',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `nationality` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_email_index` (`email`),
  KEY `users_role_index` (`role`),
  KEY `users_status_index` (`status`),
  KEY `users_company_id_index` (`company_id`),
  KEY `users_slug_index` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_user_insert_setting
        AFTER INSERT ON users
        FOR EACH ROW
        BEGIN
        DECLARE has_error BOOLEAN DEFAULT FALSE;
        DECLARE error_msg VARCHAR(255);
        DECLARE line_number INT;
    
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
            SET error_msg = CONCAT("Error in new_user_insert_setting_trigger: ", @errmsg);
            INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_user_insert_setting_trigger", "error");
            SET has_error = TRUE;
        END;
    
        INSERT INTO settings (user_id, account_access, newsletter_access, recommendations_access, announcements_access, message_from_candidate_access, display_contact_no, display_email_address, status)
        VALUES (NEW.id, "1", "1", "1", "1", "1", "1", "1", "active");
    
        IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: new_user_insert_setting_trigger", line_number, "new_user_insert_setting_trigger", "success");
        END IF;
    END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `after_users_update_password` AFTER UPDATE ON `users` FOR EACH ROW BEGIN
                -- Check if the password field is updated
                IF NEW.password <> OLD.password THEN
                    -- Send notification to the user that their password was updated
                    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
                    VALUES (NEW.id, NEW.id, CONCAT("Your password has been updated successfully."), NULL, false, "password_updated", "active", NOW(), NOW());
                END IF;
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `after_users_update_profile_image` AFTER UPDATE ON `users` FOR EACH ROW BEGIN
                -- Check if the profile_image field is updated
                IF NEW.profile_image <> OLD.profile_image THEN
                    -- Send notification to the user that their profile picture was updated
                    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
                    VALUES (NEW.id, NEW.id, CONCAT("Your profile picture has been updated successfully."), NULL, false, "profile_image_updated", "active", NOW(), NOW());
                END IF;
            END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `profile_percentage_after_update` AFTER UPDATE ON `users` FOR EACH ROW BEGIN

         DECLARE link VARCHAR(255);

            SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

            SET link = CONCAT(link, 'employees/myprofile');
   
    IF NEW.profile_complete_percentage IS NOT NULL AND OLD.profile_complete_percentage > NEW.profile_complete_percentage THEN

        INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
        SELECT 1, NEW.id, CONCAT('Congratulations, ', name, '! Your profile has been successfully updated, and it now looks better than ever before. Presenting your enhanced skills and experience, you are now poised to impress employers and seize exciting opportunities. Best of luck in your job search!'), link, false, 'register', 'active', NOW(), NOW()
        FROM users
        WHERE id = NEW.id;
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_general_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_AUTO_VALUE_ON_ZERO' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER `user_after_update` AFTER UPDATE ON `users` FOR EACH ROW BEGIN
    DECLARE notification_text VARCHAR(1000);
    DECLARE competeProfile_text VARCHAR(1000);

    -- Set the notification text based on the user's role
    IF NEW.role = 'employee' THEN
        SET notification_text = 'Welcome to our job portal! Discover exciting opportunities and take the next step in your career journey today.';
        SET competeProfile_text = 'We are excited to have you on The Talent Point job portal! Completing your profile is essential to make a lasting impression on employers. Showcase your skills, experience, and unique qualities to stand out in the job market. Don''t miss out on great opportunities – update your profile now to increase your chances of finding your dream job!';
    ELSEIF NEW.role = 'employer' THEN
        SET notification_text = 'Welcome to The Talent Point! We are thrilled to have your company on board. Get ready to tap into a pool of exceptional talent and find your perfect team to shape the future together!';
        SET competeProfile_text = 'Welcome to The Talent Point! A complete profile is the key to attracting top-tier talent. Showcase your company''s values, culture, and benefits to engage with potential candidates effectively. Increase your chances of finding the perfect fit for your organization by updating your profile now.';
    ELSEIF NEW.role = 'staff' THEN
        SET notification_text = 'Welcome to The Talent Point! As staff members, you are the driving force behind our recruitment success. Together, let''s discover exceptional talent and build a thriving team!';
        SET competeProfile_text = 'As a crucial part of our team, your professionalism is vital in building successful connections with job seekers and employers. Completing your profile will reinforce our reputation and create a positive impact on all our users. Take a moment to update your profile and elevate your presence on The Talent Point job portal.';
    END IF;

    IF NEW.role IS NOT NULL AND (OLD.role IS NULL OR NEW.role <> OLD.role) THEN

    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (1, NEW.id, notification_text, 'https://www.thetalentpoint.com', false, 'register', 'active', NOW(), NOW());

    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (1, NEW.id, competeProfile_text, 'https://www.thetalentpoint.com', false, 'register', 'active', NOW(), NOW());
    
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER profile_percentage_update
AFTER UPDATE ON users FOR EACH ROW
BEGIN

        DECLARE link VARCHAR(255);
        DECLARE has_error BOOLEAN DEFAULT FALSE;
        DECLARE error_msg VARCHAR(255);
        DECLARE line_number INT;
    
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
            SET error_msg = CONCAT("Error in profile_percentage_update_trigger: ", @errmsg);
            INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "profile_percentage_update_trigger", "error");
            SET has_error = TRUE;
        END;
            SELECT `website_url` INTO link FROM `admin_settings` WHERE `id` = 1;

            SET link = CONCAT(link, 'employees/myprofile');
   
            IF NEW.profile_complete_percentage > OLD.profile_complete_percentage THEN

        INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
        SELECT NEW.id, NEW.id, CONCAT('Congratulations, ', name, '! Your profile has been successfully updated, and it now looks better than ever before. Presenting your enhanced skills and experience, you are now poised to impress employers and seize exciting opportunities. Best of luck in your job search!'), link, false, 'register', 'active', NOW(), NOW()
        FROM users
        WHERE id = NEW.id;
    END IF;
    IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: profile_percentage_update_trigger", line_number, "profile_percentage_update_trigger", "success");
        END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER after_users_profile_fields_update
                AFTER UPDATE ON users
                FOR EACH ROW
                BEGIN
                DECLARE notification_message VARCHAR(255);
                DECLARE has_error BOOLEAN DEFAULT FALSE;
                DECLARE error_msg VARCHAR(255);
                DECLARE line_number INT;

                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
                    SET error_msg = CONCAT("Error in after_users_profile_fields_update: ", @errmsg);
                    INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "after_users_profile_fields_update", "error");
                    SET has_error = TRUE;
                END;  
                    IF NEW.password <> OLD.password THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your password has been successfully updated.", NEW.id, NEW.id, "password_updated", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.job_status <> OLD.job_status THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your job status has been successfully updated.", NEW.id, NEW.id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.bio <> OLD.bio THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your bio has been successfully updated.", NEW.id, NEW.id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;

                    IF NEW.contact_no <> OLD.contact_no THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES ("Your contact number has been successfully updated.", NEW.id, NEW.id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;
                    
                    IF NEW.facebook_link <> OLD.facebook_link THEN
                        SET notification_message = "Your Facebook link has been successfully updated.";
                    ELSEIF NEW.twitter_link <> OLD.twitter_link THEN
                        SET notification_message = "Your Twitter link has been successfully updated.";
                    ELSEIF NEW.instagram_link <> OLD.instagram_link THEN
                        SET notification_message = "Your Instagram link has been successfully updated.";
                    ELSEIF NEW.linkedin_link <> OLD.linkedin_link THEN
                        SET notification_message = "Your Linkedin link has been successfully updated.";
                    END IF;
                    IF notification_message IS NOT NULL THEN
                        INSERT INTO notifications(notification, notify_by, notify_to, notification_type, link, is_read, status, created_at, updated_at)
                        VALUES (notification_message, NEW.id, NEW.id, "message", NULL, 0, "active", NOW(), NOW());
                    END IF;
                IF NEW.profile_image <> OLD.profile_image THEN
                        INSERT INTO `notifications`(`notification`, `notify_by`, `notify_to`, `notification_type`, `link`, `is_read`, `status`, `created_at`, `updated_at`)
                        VALUES ("Your profile picture has been successfully updated.", NEW.id, NEW.id, "profile_image_updated", NULL, 0, "active", NOW(), NOW());
                    END IF;


                    IF NOT has_error THEN
                    INSERT INTO error_logs (error_message, line_number, file_name, type)
                    VALUES ("Trigger executed successfully: after_users_profile_fields_update", line_number, "after_users_profile_fields_update", "success");
                    END IF; 
                END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`root`@`localhost`*/ /*!50003 TRIGGER new_user_after_update
AFTER UPDATE ON users FOR EACH ROW
BEGIN
    DECLARE notification_text VARCHAR(1000);
    DECLARE competeProfile_text VARCHAR(1000);
    DECLARE has_error BOOLEAN DEFAULT FALSE;
        DECLARE error_msg VARCHAR(255);
        DECLARE line_number INT;
    
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            GET DIAGNOSTICS CONDITION 1 @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @errmsg = MESSAGE_TEXT;
            SET error_msg = CONCAT("Error in new_user_after_update: ", @errmsg);
            INSERT INTO error_logs (error_message, line_number, file_name, type) VALUES (error_msg, line_number, "new_user_after_update", "error");
            SET has_error = TRUE;
        END;    


    -- Set the notification text based on the user's role
    IF NEW.role = 'employee' THEN
        SET notification_text = 'Welcome to our job portal! Discover exciting opportunities and take the next step in your career journey today.';
        SET competeProfile_text = 'We are excited to have you on The Talent Point job portal! Completing your profile is essential to make a lasting impression on employers. Showcase your skills, experience, and unique qualities to stand out in the job market. Don''t miss out on great opportunities – update your profile now to increase your chances of finding your dream job!';
    ELSEIF NEW.role = 'employer' THEN
        SET notification_text = 'Welcome to The Talent Point! We are thrilled to have your company on board. Get ready to tap into a pool of exceptional talent and find your perfect team to shape the future together!';
        SET competeProfile_text = 'Welcome to The Talent Point! A complete profile is the key to attracting top-tier talent. Showcase your company''s values, culture, and benefits to engage with potential candidates effectively. Increase your chances of finding the perfect fit for your organization by updating your profile now.';
    ELSEIF NEW.role = 'staff' THEN
        SET notification_text = 'Welcome to The Talent Point! As staff members, you are the driving force behind our recruitment success. Together, let''s discover exceptional talent and build a thriving team!';
        SET competeProfile_text = 'As a crucial part of our team, your professionalism is vital in building successful connections with job seekers and employers. Completing your profile will reinforce our reputation and create a positive impact on all our users. Take a moment to update your profile and elevate your presence on The Talent Point job portal.';
    END IF;

    IF NEW.role IS NOT NULL AND (OLD.role IS NULL OR NEW.role <> OLD.role) THEN

    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (1, NEW.id, notification_text, 'https://www.thetalentpoint.com', false, 'register', 'active', NOW(), NOW());

    INSERT INTO notifications (notify_by, notify_to, notification, link, is_read, notification_type, status, created_at, updated_at)
    VALUES (1, NEW.id, competeProfile_text, 'https://www.thetalentpoint.com', false, 'register', 'active', NOW(), NOW());
    

    IF NOT has_error THEN
            INSERT INTO error_logs (error_message, line_number, file_name, type)
            VALUES ("Trigger executed successfully: new_user_after_update", line_number, "new_user_after_update", "success");
        END IF;    
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
DROP TABLE IF EXISTS `work_experience`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `work_experience` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `start_date` varchar(255) DEFAULT NULL,
  `end_date` varchar(255) DEFAULT NULL,
  `currently_work_here` varchar(255) DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `status` enum('pending','active','deleted') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` VALUES (3,'2018_08_08_100000_create_telescope_entries_table',1);
INSERT INTO `migrations` VALUES (4,'2019_03_01_050741_create_errorlog_table',1);
INSERT INTO `migrations` VALUES (5,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` VALUES (6,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` VALUES (7,'2023_04_17_132541_create_company_table',1);
INSERT INTO `migrations` VALUES (8,'2023_04_17_132725_create_jobs_table',1);
INSERT INTO `migrations` VALUES (9,'2023_04_17_132725_create_jobs_view_table',1);
INSERT INTO `migrations` VALUES (10,'2023_04_17_132745_create_messages_table',1);
INSERT INTO `migrations` VALUES (11,'2023_04_17_132808_create_membership_table',1);
INSERT INTO `migrations` VALUES (12,'2023_04_17_132808_create_payment_table',1);
INSERT INTO `migrations` VALUES (13,'2023_04_17_132808_create_plans_table',1);
INSERT INTO `migrations` VALUES (14,'2023_04_17_132839_create_resumes_table',1);
INSERT INTO `migrations` VALUES (15,'2023_04_17_132839_create_resumes_viewed_table',1);
INSERT INTO `migrations` VALUES (16,'2023_04_17_132903_create_applications_table',1);
INSERT INTO `migrations` VALUES (17,'2023_04_17_133005_create_settings_table',1);
INSERT INTO `migrations` VALUES (18,'2023_04_17_133034_create_interviews_table',1);
INSERT INTO `migrations` VALUES (19,'2023_04_17_133220_create_company_followers_table',1);
INSERT INTO `migrations` VALUES (20,'2023_04_17_133220_create_saved_jobs_table',1);
INSERT INTO `migrations` VALUES (21,'2023_04_17_133305_create_availability_table',1);
INSERT INTO `migrations` VALUES (22,'2023_04_17_133347_create_admin_settings_table',1);
INSERT INTO `migrations` VALUES (23,'2023_04_17_133347_create_inquires_table',1);
INSERT INTO `migrations` VALUES (24,'2023_04_17_133347_create_sector_table',1);
INSERT INTO `migrations` VALUES (25,'2023_04_17_133417_create_locations_table',1);
INSERT INTO `migrations` VALUES (26,'2023_04_17_133450_create_portfolio_table',1);
INSERT INTO `migrations` VALUES (27,'2023_04_18_120743_create_work_experience_table',1);
INSERT INTO `migrations` VALUES (28,'2023_04_18_120834_create_education_table',1);
INSERT INTO `migrations` VALUES (29,'2023_04_18_120853_create_employee_skills_table',1);
INSERT INTO `migrations` VALUES (30,'2023_04_18_120853_create_skills_table',1);
INSERT INTO `migrations` VALUES (31,'2023_04_18_120916_create_languages_table',1);
INSERT INTO `migrations` VALUES (32,'2023_04_20_121817_create_company_profile_view_table',1);
INSERT INTO `migrations` VALUES (33,'2023_06_16_070634_create_countries_table',1);
INSERT INTO `migrations` VALUES (34,'2023_07_19_065313_create_notifications_table',1);
INSERT INTO `migrations` VALUES (35,'2023_07_20_041049_create_user_after_update_trigger',1);
INSERT INTO `migrations` VALUES (36,'2023_07_20_045245_create_user_after_update_profile_percentage',1);
INSERT INTO `migrations` VALUES (37,'2023_07_20_050337_create_interview_after_interview_insert',1);
INSERT INTO `migrations` VALUES (38,'2023_07_20_054308_create_profile_image_updated_notification_trigger',1);
INSERT INTO `migrations` VALUES (39,'2023_07_20_054426_create_password_updated_notification_trigger',1);
INSERT INTO `migrations` VALUES (40,'2023_07_20_060625_create_after_saved_jobs_insert_trigger',1);
INSERT INTO `migrations` VALUES (41,'2023_07_20_060651_create_after_company_followers_insert_trigger',1);
INSERT INTO `migrations` VALUES (42,'2023_07_20_060839_create_after_company_profile_view_insert_trigger',1);
INSERT INTO `migrations` VALUES (43,'2023_07_21_050651_create_industries_table',1);
INSERT INTO `migrations` VALUES (44,'2023_07_22_041646_create_blog_categories_table',1);
INSERT INTO `migrations` VALUES (45,'2023_07_22_041740_create_blogs_table',1);
INSERT INTO `migrations` VALUES (46,'2023_07_25_100625_create_job_filters_table',1);
INSERT INTO `migrations` VALUES (47,'2023_07_27_112118_create_after_blog_insert_trigger',1);
INSERT INTO `migrations` VALUES (48,'2023_07_27_122529_create_candidates_filters_table',1);
INSERT INTO `migrations` VALUES (49,'2023_07_28_055301_create_default_experience',1);
INSERT INTO `migrations` VALUES (50,'2023_07_29_085534_create_user_after_skills_trigger',1);
INSERT INTO `migrations` VALUES (51,'2023_07_29_091328_create_employee_skill_insert',1);
INSERT INTO `migrations` VALUES (52,'2023_08_28_091205_create_interview_update_trigger',1);
INSERT INTO `migrations` VALUES (53,'2023_06_16_070634_create_cities_table',2);
INSERT INTO `migrations` VALUES (54,'2023_09_20_035806_add_job_city_to_jobs_table',2);
INSERT INTO `migrations` VALUES (55,'2023_09_20_035807_add_nationality_to_users_table',2);
INSERT INTO `migrations` VALUES (56,'2023_09_06_043325_create_drop_trigger',3);
INSERT INTO `migrations` VALUES (57,'2023_09_06_072907_create__error_logs_table',3);
INSERT INTO `migrations` VALUES (58,'2023_09_06_073108_create_new_user_setting_trigger',3);
INSERT INTO `migrations` VALUES (59,'2023_09_06_083429_create_profile_percentage_trigger',3);
INSERT INTO `migrations` VALUES (60,'2023_09_06_084418_create_insert_blog_trigger',3);
INSERT INTO `migrations` VALUES (61,'2023_09_06_085227_create_apply_job_trigger',3);
INSERT INTO `migrations` VALUES (62,'2023_09_06_090317_create_interview_insert_trigger',3);
INSERT INTO `migrations` VALUES (63,'2023_09_06_091252_create_user_profile_update_trigger',3);
INSERT INTO `migrations` VALUES (64,'2023_09_06_092920_create_employee_skills_trigger',3);
INSERT INTO `migrations` VALUES (65,'2023_09_06_093933_create_company_profile_view_trigger',3);
INSERT INTO `migrations` VALUES (66,'2023_09_06_094926_create_company_followers_trigger',3);
INSERT INTO `migrations` VALUES (67,'2023_09_06_095556_create_save_job_trigger',3);
INSERT INTO `migrations` VALUES (68,'2023_09_06_100845_create_user_update_trigger',3);
