<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('work_flows', function (Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->nullable()->after('instance');
            $table->string('AND_OR')->nullable()->after('parent_id');
            $table->unsignedBigInteger('condition_data_point')->nullable()->after('AND_OR');
            $table->tinyInteger('condition_status')->nullable()->after('condition_data_point');
            $table->string('contacts')->nullable()->after('condition_status');
            $table->tinyInteger('segment')->nullable()->after('contacts');
            $table->string('segment_condition')->nullable()->after('segment');
            $table->string('segment_name')->nullable()->after('segment_condition');
            $table->string('segment_value')->nullable()->after('segment_name');
            $table->string('frequency_value')->nullable()->after('segment_value');
            $table->string('frequency_period')->nullable()->after('frequency_value');
            $table->string('frequency_time')->nullable()->after('frequency_period');
            $table->string('AM_PM')->nullable()->after('frequency_time');
            $table->string('start_date')->nullable()->after('AM_PM');
            $table->string('start_time')->nullable()->after('start_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('work_flows', function (Blueprint $table) {
            $table->dropColumn('parent_id');
            $table->dropColumn('AND_OR');
            $table->dropColumn('condition_data_point');
            $table->dropColumn('condition_status');
            $table->dropColumn('contacts');
            $table->dropColumn('segment');
            $table->dropColumn('segment_condition');
            $table->dropColumn('segment_name');
            $table->dropColumn('segment_value');
            $table->dropColumn('frequency_value');
            $table->dropColumn('frequency_period');
            $table->dropColumn('frequency_time');
            $table->dropColumn('AM_PM');
            $table->dropColumn('start_date');
            $table->dropColumn('start_time');
        });
    }
};
