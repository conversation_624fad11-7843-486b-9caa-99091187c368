<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('interviews', function (Blueprint $table) {
            $table->date('interview_schedule_date_end')->nullable()->after('interview_schedule_date');
            $table->string('company_location')->nullable()->after('interview_status');
            $table->text('brief_message')->nullable()->after('company_location');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('interviews', function (Blueprint $table) {
            $table->dropColumn('interview_schedule_date_end');
            $table->dropColumn('company_location');
            $table->dropColumn('brief_message');
        });
    }
};
