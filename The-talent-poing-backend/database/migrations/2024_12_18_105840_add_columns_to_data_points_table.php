<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('data_points', function (Blueprint $table) {
            $table->string('relation_name')->nullable()->after('data_point_name');
            $table->string('column_name')->nullable()->after('relation_name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('data_points', function (Blueprint $table) {
            $table->dropColumn('relation_name');
            $table->dropColumn('column_name');
        });
    }
};
