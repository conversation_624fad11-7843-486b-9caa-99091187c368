<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('data_points', function (Blueprint $table) {
            $table->id();
            $table->string('data_point_name')->nullable();
            $table->string('user_type')->nullable()->comment('1=employee, 2=employer');
            $table->string('data_type')->nullable();
            $table->string('status')->nullable()->comment('0=inactive, 2=active');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('data_points');
    }
};
