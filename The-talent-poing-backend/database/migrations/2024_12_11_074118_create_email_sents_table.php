<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_sents', function (Blueprint $table) {
            $table->id();
            $table->string('user_email')->nullable();
            $table->unsignedBigInteger('workflow_id')->nullable();
            $table->tinyInteger('is_sent')->nullable()->comment('0=>no, 1=>yes');
            $table->tinyInteger('is_viewed')->nullable()->comment('0=>no, 1=>yes');
            $table->tinyInteger('is_spam')->nullable()->comment('0=>no, 1=>yes');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('email_sents');
    }
};
