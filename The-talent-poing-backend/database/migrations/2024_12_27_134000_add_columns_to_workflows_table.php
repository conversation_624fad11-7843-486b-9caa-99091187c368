<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('workflows', function (Blueprint $table) {
            // $table->unsignedBigInteger('secondary_condition_data_point')->nullable()->after('condition_value');
            // $table->unsignedBigInteger('secondary_condition_operator')->nullable()->after('secondary_condition_data_point');
            // $table->unsignedBigInteger('secondary_condition_value')->nullable()->after('secondary_condition_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('workflows', function (Blueprint $table) {
            // $table->dropColumn('secondary_condition_data_point');
            // $table->dropColumn('secondary_condition_status');
            // $table->dropColumn('secondary_condition_value');
        });
    }
};
