<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('workflow_segments', function (Blueprint $table) {
            $table->id(); // Auto-increment primary key
            $table->foreignId('workflow_id')->constrained('work_flows')->onDelete('cascade'); // Foreign key to workflows table
            $table->string('datapoint_id'); // Field name (e.g., age, status)
            $table->enum('operator', ['=', '!=', '<', '>', '<=', '>=', 'LIKE', 'IS NULL', 'IS NOT NULL', 'IN', 'BETWEEN']); // Supported operators
            $table->string('value')->nullable(); // Value for comparison (nullable for IS NULL/IS NOT NULL)
            $table->enum('connector', ['AND', 'OR'])->nullable(); // Connector (AND/OR, nullable for the last condition)
            $table->timestamps(); // created_at and updated_at
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('workflow_segments');
    }
};
