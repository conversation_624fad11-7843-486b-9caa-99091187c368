<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('files', function (Blueprint $table) {
            $table->uuid()->primary()->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('code')->default('public')->nullable();
            $table->text('source');
            $table->string('thumbnail', 255)->nullable();
            $table->string('type', 100)->nullable();
            $table->string('size', 10)->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
        Schema::table('files', function (Blueprint $table) {
            $table->foreignUuid('fk_original_file_uuid')
                ->after('size')
                ->nullable()
                ->constrained('files', 'uuid')
                ->cascadeOnDelete();
        });

        Schema::create('file_activities', function (Blueprint $table) {
            $table->uuid()->unique()->primary();
            $table->string('action');
            $table->text('comment')->nullable();
            $table->timestamp('requested_at')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('fk_user_id');
            $table->foreign('fk_user_id')->references('id')->on('users');
            $table->foreignUuid('fk_file_uuid')
                ->constrained('files', 'uuid')
                ->cascadeOnDelete();
            $table->timestamps();
        });

        Schema::table('company', function (Blueprint $table) {
            $table->foreignUuid('fk_logo_file_uuid')
                ->after('company_description')
                ->nullable()
                ->constrained('files', 'uuid')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('files');
        Schema::dropIfExists('file_activities');
    }
};
