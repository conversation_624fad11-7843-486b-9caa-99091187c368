<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('schedule_available_from_day')->nullable()->default('Monday');
            $table->string('schedule_available_to_day')->nullable()->default('Friday');
            $table->string('schedule_available_time_range')->nullable()->default('10:00AM - 07:00PM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('schedule_available_from_day');
            $table->dropColumn('schedule_available_to_day');
            $table->dropColumn('schedule_available_time_range');
        });
    }
};
