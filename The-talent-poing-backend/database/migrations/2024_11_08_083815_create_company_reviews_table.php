<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_reviews', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('user_id');
            $table->string('rating');
            $table->string('review_title');
            $table->longText('review_description');
            $table->tinyInteger('is_approve')->default(0);
            $table->tinyInteger('is_reported')->default(0);
            $table->unsignedBigInteger('helpful_yes_count')->default(0);
            $table->unsignedBigInteger('helpful_no_count')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_reviews');
    }
};
