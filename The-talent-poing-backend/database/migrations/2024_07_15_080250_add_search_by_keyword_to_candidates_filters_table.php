<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('candidates_filters', function (Blueprint $table) {
            $table->string('search_by_keyword')->nullable(); // Add your new column here
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('candidates_filters', function (Blueprint $table) {
            $table->dropColumn('search_by_keyword'); // Drop the column if rolling back
        });
    }
};
