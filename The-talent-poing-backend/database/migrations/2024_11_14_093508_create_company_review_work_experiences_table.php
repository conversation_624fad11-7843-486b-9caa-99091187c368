<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_review_work_experiences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('review_id');
            // $table->unsignedBigInteger('user_id');
            // $table->unsignedBigInteger('company_id');
            $table->string('job_title')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('start_date')->nullable();
            $table->string('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_review_work_experiences');
    }
};
